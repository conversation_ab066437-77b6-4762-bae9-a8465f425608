from fastapi import APIRouter

from app.api.v1.endpoints.example import router as example_router
from app.api.v1.endpoints.knownledge_base import router as knowledge_base_router
from app.api.v1.endpoints.test_generate import router as test_generate_router

api_router = APIRouter()

api_router.include_router(knowledge_base_router, prefix="/knowledge_base", tags=["知识库"])
api_router.include_router(example_router, prefix="/example", tags=["示例API"])
api_router.include_router(test_generate_router, prefix="/test-generate", tags=["考题生成"])