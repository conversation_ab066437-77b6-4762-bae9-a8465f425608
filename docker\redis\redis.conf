# Redis配置文件
# 网络配置
bind 0.0.0.0
port 6379
protected-mode no

# 一般配置
daemonize no
loglevel notice
logfile ""

# 数据库配置
databases 16
save 900 1
save 300 10
save 60 10000

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 慢查询配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 安全配置
# requirepass yourpassword  # 开发环境可以不设置密码

# 其他配置
dir /data
rdbcompression yes
rdbchecksum yes
stop-writes-on-bgsave-error yes