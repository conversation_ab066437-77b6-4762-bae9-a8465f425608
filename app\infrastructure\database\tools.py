from typing import Optional, List, Dict, Any
from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.infrastructure.database.session import db_manager
from app.core.exceptions import DatabaseException


class DatabaseMigration:
    """数据库迁移工具"""
    
    def __init__(self):
        self.pg_session = db_manager.get_pg_session
        self.mysql_session = db_manager.get_mysql_session
    
    def create_migration(self, name: str, up_sql: str, down_sql: str) -> Dict[str, Any]:
        """创建迁移"""
        migration = {
            "name": name,
            "up_sql": up_sql,
            "down_sql": down_sql,
            "created_at": "datetime.utcnow()",
        }
        
        # 这里可以保存到迁移表中
        return migration
    
    def run_migration(self, migration: Dict[str, Any], database: str = "postgres") -> bool:
        """执行迁移"""
        try:
            session_factory = self.pg_session if database == "postgres" else self.mysql_session
            
            with session_factory() as session:
                session.execute(text(migration["up_sql"]))
                session.commit()
                
                # 记录迁移历史
                self._record_migration(session, migration["name"])
                
                return True
        except SQLAlchemyError as e:
            raise DatabaseException(f"执行迁移失败: {str(e)}") from e
    
    def rollback_migration(self, migration: Dict[str, Any], database: str = "postgres") -> bool:
        """回滚迁移"""
        try:
            session_factory = self.pg_session if database == "postgres" else self.mysql_session
            
            with session_factory() as session:
                session.execute(text(migration["down_sql"]))
                session.commit()
                
                # 删除迁移历史
                self._remove_migration_record(session, migration["name"])
                
                return True
        except SQLAlchemyError as e:
            raise DatabaseException(f"回滚迁移失败: {str(e)}") from e
    
    def _record_migration(self, session: Session, name: str):
        """记录迁移历史"""
        # 这里需要有一个迁移历史表
        pass
    
    def _remove_migration_record(self, session: Session, name: str):
        """删除迁移记录"""
        # 这里需要有一个迁移历史表
        pass
    
    def get_applied_migrations(self, database: str = "postgres") -> List[str]:
        """获取已应用的迁移"""
        try:
            session_factory = self.pg_session if database == "postgres" else self.mysql_session
            
            with session_factory() as session:
                # 查询迁移历史表
                # 这里需要实际的迁移历史表
                return []
        except SQLAlchemyError as e:
            raise DatabaseException(f"获取迁移历史失败: {str(e)}") from e


class DatabaseSeeder:
    """数据库种子数据工具"""
    
    def __init__(self):
        self.pg_session = db_manager.get_pg_session
        self.mysql_session = db_manager.get_mysql_session
    
    def seed_initial_data(self, database: str = "postgres"):
        """种子初始数据"""
        session_factory = self.pg_session if database == "postgres" else self.mysql_session
        
        try:
            with session_factory() as session:
                # 创建默认用户
                self._create_default_users(session)
                
                # 创建默认知识库
                self._create_default_knowledge_bases(session)
                
                session.commit()
        except SQLAlchemyError as e:
            raise DatabaseException(f"种子数据创建失败: {str(e)}") from e
    
    def _create_default_users(self, session: Session):
        """创建默认用户"""
        # 这里需要用户模型
        pass
    
    def _create_default_knowledge_bases(self, session: Session):
        """创建默认知识库"""
        # 这里需要知识库模型
        pass


class DatabaseAnalyzer:
    """数据库分析工具"""
    
    def __init__(self):
        self.pg_session = db_manager.get_pg_session
        self.mysql_session = db_manager.get_mysql_session
    
    def analyze_table_sizes(self, database: str = "postgres") -> List[Dict[str, Any]]:
        """分析表大小"""
        session_factory = self.pg_session if database == "postgres" else self.mysql_session
        
        try:
            with session_factory() as session:
                if database == "postgres":
                    query = text("""
                        SELECT 
                            schemaname,
                            tablename,
                            attname,
                            n_distinct,
                            correlation
                        FROM pg_stats
                        WHERE schemaname = 'public'
                        ORDER BY tablename, attname;
                    """)
                else:
                    query = text("""
                        SELECT 
                            table_schema,
                            table_name,
                            column_name,
                            data_type,
                            is_nullable
                        FROM information_schema.columns
                        WHERE table_schema = DATABASE()
                        ORDER BY table_name, column_name;
                    """)
                
                result = session.execute(query)
                return [dict(row) for row in result.fetchall()]
        except SQLAlchemyError as e:
            raise DatabaseException(f"分析表大小失败: {str(e)}") from e
    
    def analyze_query_performance(self, query: str, database: str = "postgres") -> Dict[str, Any]:
        """分析查询性能"""
        session_factory = self.pg_session if database == "postgres" else self.mysql_session
        
        try:
            with session_factory() as session:
                if database == "postgres":
                    explain_query = text(f"EXPLAIN ANALYZE {query}")
                else:
                    explain_query = text(f"EXPLAIN {query}")
                
                result = session.execute(explain_query)
                return {
                    "query": query,
                    "explain": [dict(row) for row in result.fetchall()]
                }
        except SQLAlchemyError as e:
            raise DatabaseException(f"分析查询性能失败: {str(e)}") from e
    
    def check_index_usage(self, database: str = "postgres") -> List[Dict[str, Any]]:
        """检查索引使用情况"""
        session_factory = self.pg_session if database == "postgres" else self.mysql_session
        
        try:
            with session_factory() as session:
                if database == "postgres":
                    query = text("""
                        SELECT 
                            indexrelname as index_name,
                            relname as table_name,
                            idx_scan,
                            idx_tup_read,
                            idx_tup_fetch
                        FROM pg_stat_user_indexes
                        ORDER BY idx_scan DESC;
                    """)
                else:
                    query = text("""
                        SELECT 
                            TABLE_NAME,
                            INDEX_NAME,
                            CARDINALITY,
                            SUB_PART
                        FROM information_schema.STATISTICS
                        WHERE TABLE_SCHEMA = DATABASE()
                        ORDER BY TABLE_NAME, INDEX_NAME;
                    """)
                
                result = session.execute(query)
                return [dict(row) for row in result.fetchall()]
        except SQLAlchemyError as e:
            raise DatabaseException(f"检查索引使用情况失败: {str(e)}") from e


# 全局工具实例
migration_tool = DatabaseMigration()
seeder_tool = DatabaseSeeder()
analyzer_tool = DatabaseAnalyzer()