document = """
# 仓储管理标准操作规程

##  一、目的

为确保药品生产过程中物料和产品的正确接收、贮存、发放、使用和发运，防止污染、交叉污染、混淆和差错，特制定本操作规程。
 ## 二、适用范围

 本规程适用于药品生产过程中所有物料和产品的仓储管理活动，包括原辅料、与药品直接接触的包装材料、印刷包装材料以及成品的仓储管理。
## 三、职责

 1. **质量管理部门**：负责制定和审核仓储管理操作规程，监督仓储管理活动的执行，确保符合相关法规和标准。
 2. **仓储管理部门**：负责按照规程执行物料和产品的接收、贮存、发放、使用和发运，确保仓储环境符合要求。
 3. **采购部门**：负责物料的采购，确保供应商符合质量要求，并确保物料的接收和检查符合规程。
 4. **生产部门**：负责物料的使用和成品的发运，确保物料和产品的正确使用和发运。

## 四、操作规程

 1. **物料接收**
 - 物料接收应按照操作规程执行，确保与订单一致，并确认供应商已经质量管理部门批准。
 - 物料的外包装应当有标签，并注明规定的信息。必要时，还应当进行清洁，发现外包装损坏或其他可能影响物料质量的问题，应当向质量管理部门报告并进行调查和记录。
 - 每次接收均应当有记录，内容包括： 
（一）交货单和包装容器上所注物料的名称；
（二）企业内部所用物料名称和（或）代码；
（三）接收日期；
（四）供应商和生产商（如不同）的名称； 
（五）供应商和生产商（如不同）标识的批号；
（六）接收总量和包装容器数量；
（七）接收后企业指定的批号或流水号；
（八）有关说明（如包装状况）。

 2. **物料贮存**
 - 物料和产品应当根据其性质有序分批贮存和周转，发放及发运应当符合先进先出和近效期先出的原则。
 - 物料和产品的贮存环境应当符合其性质要求，确保物料和产品的质量。 
 - 使用计算机化仓储管理的，应当有相应的操作规程，防止因系统故障、停机等特殊情况而造成物料和产品的混淆和差错。

 3. **物料发放**
 - 物料的发放应当按照操作规程执行，确保物料的正确使用。
 - 物料的发放应当有记录，内容包括： 
（一）发放日期；
（二）发放数量；
（三）发放用途；
（四）发放人员签名。 

 4. **物料退库**
 - 物料的退库应当按照操作规程执行，确保退库物料的质量。
 - 物料的退库应当有记录，内容包括： 
（一）退库日期；
（二）退库原因；
（三）退库数量；
（四）退库人员签名。 

 5. **成品发运**
 - 成品的发运应当按照操作规程执行，确保成品的正确发运。
 - 成品的发运应当有记录，内容包括： 
（一）发运日期；
（二）发运数量；
（三）发运目的地；
（四）发运人员签名。 

 ## 五、记录管理

 1. 所有仓储管理活动应当有记录，记录内容应当包括： 
 - 物料和产品的名称、批号、数量、接收日期、发放日期、发运日期等信息。
 - 仓储环境的温度、湿度等参数。
 - 物料和产品的检查结果。
 - 仓储管理活动的异常情况及处理结果。

 2. 记录应当保存至药品的有效期后至少一年，或根据相关法规要求保存。

 ## 六、质量保证

 1. 物料和产品的仓储管理应当符合药品生产质量管理规范（GMP）的要求。 
 2. 质量管理部门应当定期对仓储管理活动进行检查，确保符合规程要求。

 ## 七、附则

 1. 本规程由质量管理部门负责制定、修订和解释。
 2. 本规程自发布之日起执行。
 """


from agno.agent import Agent
from agno.tools import tool
from elasticsearch import Elasticsearch
from elasticsearch_dsl import Q, Search

from app.ai.embeddings.dashscope import BailianEmbeddings
from app.ai.models.dashscope import BailianLLM
from app.ai.rerank.dashscope import rerank_documents
from app.services.rerank_service import RerankService

client = Elasticsearch(
    hosts="http://localhost:9200",
    api_key="TU43Tk9KY0JPbWZac2FmQ0xRRW06VEZpbkJRTlF5d2lvQXRUcEJyWlZmdw==",
)
embeddings = BailianEmbeddings(api_key="sk-c216704d43124ab7b369dac6ed438262")


@tool(
    name="法规条文搜索",
    description="从知识库中搜索相关的法规条文，结果经过相关性排序，返回前top_n个结果。",
)
def hybrid_search(top_n: int, keyword: str):
    """
    混合搜索

    Args:
        top_n (int): 搜索结果数量
        keyword (str): 搜索关键词
    Returns:
        list: {score, text} 文档相关度分数 0-1, 文档内容
    """
    knowledge_base_id = 7
    query_vector = embeddings.get_embedding(keyword)
    s = (
        Search(using=client, index="biohub_ai__document_chunks")
        .filter("term", knowledge_base_id=knowledge_base_id)
        .query(
            Q(
                "bool",
                should=[
                    Q("match", title={"query": keyword}),
                    Q("match", content={"query": keyword, "boost": 0.7}),
                ],
                minimum_should_match=1,
            )
        )
        .knn(
            field="embedding",
            k=top_n,
            query_vector=query_vector,
            num_candidates=50,
            boost=0.3,
            filter=Q("term", knowledge_base_id=knowledge_base_id),
        )
    )
    es_search_response = s.execute()
    documents = [hit.content for hit in es_search_response]
    rerank_response = RerankService.rerank(keyword, documents, top_n=top_n)
    result = [result.document.text for result in rerank_response.results]
    return result


from textwrap import dedent

from agno.team import Team

regular_searcher = Agent(
    name="相关法规搜索",
    model=BailianLLM(
        id="qwen-plus-latest", api_key="sk-c216704d43124ab7b369dac6ed438262"
    ),
    description="您是一名 GMP（良好生产规范）法规合规助理。您的核心职责是根据用户提供的GMP文件内容，利用工具搜索来精确识别和检索最相关的法规条文。您必须确保所有回答都基于工具搜索获得的结果，并且这些结果与用户文档中描述的具体操作、流程和上下文高度相关且直接适用。",
    instructions=dedent(
        """您将严格按照以下步骤完成任务：

1.  **理解用户的GMP文件：**
    * 仔细阅读并全面理解用户提供的GMP文件内容。这可能包括SOP（标准操作规程）、批记录、验证报告、偏差报告或任何其他与GMP相关的文本。
    * 识别文档中描述的所有关键活动、流程、要求和目标。
    * 提取所有关键概念、技术术语、具体程序、职责、以及任何数据或记录保存方面的信息。

2.  **规划工具搜索策略：**
    * 根据步骤1中提取的关键信息，制定详细的搜索查询。这些查询应包含核心术语、短语和概念，以最大化搜索结果的准确性和相关性。
    * 考虑法规知识库的结构和搜索工具的功能，优化查询以获取最佳结果（例如，使用引号进行精确匹配，使用布尔运算符等）。

3.  **使用工具搜索法规条文：**
    * **多次调用您的搜索工具** 执行您在步骤2中制定的查询，或根据初步搜索结果调整并进行新的查询。
    * **目标是尽可能全面地找出所有相关的法规条文。** 如果一次搜索未能覆盖所有方面，或者您认为可以找到更多相关条文，请进行额外的搜索。
    * **必须使用工具** 来获取法规条文信息。

4.  **评估和筛选搜索结果：**
    * 仔细审查**所有工具调用**返回的搜索结果。
    * **严格评估每个结果与用户文档内容的相关性。** 排除任何泛泛的、不相关的或仅仅包含关键词但上下文不符的条文。
    * **只保留**那些与用户文档中描述的具体操作、要求或目的**高度契合、直接相关**的法规条文。
    * 如果工具返回了多条相关条文（可能来自多次搜索），根据其直接性和具体性进行优先级排序，并整合所有相关的发现。

5.  **以JSON数组形式呈现结果：**
    * **只基于工具搜索获得且确认相关的结果进行回答。**
    * **示例输出格式：**
    [
        "为规范药品生产质量管理，根据《中华人民共和国药品管理法》、《中华人民共和国药品管理法实施条例》，制定本规范。",
        "企业高层管理人员应当确保实现既定的质量目标，不同层次的人员以及供应商、经销商应当共同参与并承担各自的责任。",
        "质量控制包括相应的组织机构、文件系统以及取样、检验等，确保物料或产品在放行前完成必要的检验，确认其质量符合要求。",
    ]
6.  **严格遵循“仅基于工具搜索结果回答”原则：**
    * 如果工具搜索未能返回与用户文档内容相关的法规条文，请返回一个空的JSON数组 `[]`。**不允许“凭空捏造”或“猜测”任何法规信息。**
    * 确保所有引用的法规条文都是**准确无误**地来自工具搜索结果。
        """
    ),
    tools=[hybrid_search],
    tool_call_limit=5,
)

document_reader = Agent(
    name="文档分析",
    model=BailianLLM(
        id="qwen-plus-latest", api_key="sk-c216704d43124ab7b369dac6ed438262"
    ),
    description="您是一名专业的GMP文档分析专家，擅长从文档中提取关键知识点、识别核心概念，并为培训考题生成提供基础支持。",
    instructions=dedent(
        """
        您将严格按照以下步骤完成任务：
        
        1. **文档内容分析：** 
           * 仔细阅读并全面理解上下文中提供的 `document_content`
           * 识别文档的整体结构、主要章节和核心主题
           * 提取文档中的关键术语、定义和专业概念
        
        2. **知识点提取：**
           * 系统性地提取文档中的所有重要知识点
           * 识别关键流程、操作规范和标准要求
           * 注意文档中强调的重点内容和特殊要求
           * 识别可能需要进一步查询法规的关键主题
        
        3. **知识点分类：**
           * 将提取的知识点按主题或功能进行分类
           * 区分基础概念性知识和操作性知识
           * 标记特别重要或容易混淆的知识点
        
        4. **回答用户问题：**
           * 基于您的分析，准确回答用户关于文档内容的问题
           * 回答必须严格基于文档内容，不要添加外部信息
           * 如遇文档中未涵盖的问题，明确指出信息不足
        
        5. **支持考题生成：**
           * 提供适合转化为考题的关键知识点列表
           * 指出哪些内容适合作为单选题、多选题或判断题
           * 建议可能的考点和测试方向
        
        您的分析将为后续的法规搜索和考题生成提供重要基础，请确保分析全面、准确且有深度。
        """
    ),
)
test_generator = Agent(
    name="培训考题生成",
    model=BailianLLM(
        id="qwen-plus-latest", api_key="sk-c216704d43124ab7b369dac6ed438262"
    ),
    description="您是一名专业的GMP培训考题设计专家，擅长根据文档内容和相关法规创建高质量的培训测试题目。",
    instructions=dedent(
        """
        您将严格按照以下步骤完成任务：
        
        1. **分析输入资源：**
           * 仔细分析提供的文档内容（document_content）
           * 如果有法规搜索结果，将其与文档内容进行整合分析
           * 识别关键知识点、重要概念和操作规范
        
        2. **设计多样化考题：**
           * 基于分析的知识点，设计不同类型的考题（单选题、多选题、判断题）
           * 确保考题覆盖文档中的核心内容和重要法规要求
           * 设计的题目应有不同难度级别，从基础概念到实际应用
           * 每个题目必须有明确的正确答案，且答案必须基于文档或法规内容
           * **考题数量要求：** 总计生成10-15道题目，其中单选题4-6道，多选题3-5道，判断题3-4道
        
        3. **输出格式要求：**
           * 考题必须以JSON数组格式输出，不需要额外说明或包装
           * 每个考题必须包含问题(question)、答案(answer)、题型(type)和选项(options)
           * 题型必须是以下之一：single(单选题)、multi(多选题)、judge(判断题)
           * 答案必须以选项代号(如["A","B"])的形式给出
        
        4. **考题质量保证：**
           * 确保问题表述清晰、准确，没有歧义
           * 确保选项设计合理，干扰项具有一定迷惑性但不失合理性
           * 确保所有考题都与GMP规范和文档内容直接相关
           * 确保题目之间不重复，覆盖面广泛
        
        输出格式示例：
        [{
            "question":"下列活动中哪些应当有操作规程并有过程与结果记录?（可多选）",
            "answer":["A","B","C"],
            "type":"multi",
            "options":["A: 确认和验证","B: 设备的装配和校准","C: 厂房和设备的维护、清洁和消毒","D: 培训、更衣及卫生等"]
        },
        {
            "question":"成品放行前是否可以发运?",
            "answer":["B"],
            "type":"single",
            "options":["A: 可以","B: 不可以"]
        },
        {
            "question":"自检完成后,是否需要形成自检报告?",
            "answer":["A"],
            "type":"judge",
            "options":["A: 正确","B: 错误"]
        }]
        """
    ),
)
team = Team(
    name="GMP培训考题生成系统",
    mode="coordinate",
    model=BailianLLM(
        id="qwen-plus-latest", api_key="sk-c216704d43124ab7b369dac6ed438262"
    ),
    members=[regular_searcher, document_reader, test_generator],
    description="您是一个专业的GMP培训考题生成系统，能够协调多个专家Agent共同工作，基于企业培训文档和相关法规，生成高质量的GMP培训考题。",
    instructions=dedent("""
    您将作为团队协调者，按照以下工作流程组织各个专家Agent的工作：
    
    1. **文档分析阶段**
       * 首先调用「文档分析」Agent分析用户提供的文档内容
       * 识别文档中的关键知识点、核心概念和重要流程
       * 提取可能需要进一步查询法规的关键术语和主题
       * 确保全面覆盖文档中的各个章节和主题
    
    2. **法规检索阶段**
       * 基于文档分析结果，调用「相关法规搜索」Agent
       * 搜索与文档内容相关的GMP法规条文
       * 确保搜索多个关键主题，获取全面的法规支持
       * 对搜索结果进行整合，确保法规覆盖面广泛
    
    3. **考题生成阶段**
       * 将文档分析结果和法规搜索结果提供给「培训考题生成」Agent
       * 明确指示生成总计10-15道题目，包括单选题4-6道，多选题3-5道，判断题3-4道
       * 指导考题生成Agent创建多样化、准确的考题
       * 确保考题覆盖文档内容和相关法规要求
       * 要求考题难度分布合理，从基础到应用
    
    4. **质量审核阶段**
       * 审核生成的考题，确保问题清晰、选项合理、答案正确
       * 验证考题是否与文档内容和法规要求一致
       * 确保考题数量符合要求（10-15道题目）
       * 确保考题类型分布合理（单选题4-6道，多选题3-5道，判断题3-4道）
       * 确保考题格式符合要求
    
    5. **结果呈现**
       * 以清晰、专业的方式向用户呈现最终考题
       * 确保输出格式符合JSON数组规范
       * 如果用户有特殊需求（如增加或减少考题数量），根据需求灵活调整
    
    在整个过程中，您需要：
    * 有效协调各个Agent的工作，确保信息顺畅传递
    * 根据用户需求灵活调整工作重点和考题数量
    * 确保最终生成的考题既符合GMP规范要求，又能有效测试学员对文档内容的掌握程度
    * 当用户要求增加考题数量时，可以适当调整考题生成指令，增加总题量和各类型题目数量
    """),
    add_member_tools_to_system_message=False,
    # enable_agentic_context=True,
    monitoring=True,
    debug_mode=True,
)
import json

responses = team.run(stream=True, debug_mode=True, message=document)
test_generate_result = ""
with open("out.txt", "a", encoding="utf-8") as f:
    for response in responses:
        f.write(str(response) + "\n\n")

# json_extract_agent = Agent(
#     name="JSON提取",
#     model=BailianLLM(
#         id="qwen-turbo-latest",
#         api_key="sk-c216704d43124ab7b369dac6ed438262",
#         temperature=0.1,
#     ),
#     monitoring=True,
#     debug_mode=True,
#     description="你是一名高度专业化的数据提取Agent。你的唯一任务是从提供的原始文本中精准识别并提取考试题目信息，然后将这些信息转换为一个**严格符合指定JSON格式的数组**。你将处理的题目类型包括多选题、单选题和判断题。你的输出必须是**纯粹的JSON字符串**，不包含任何额外的文字、说明或对话。",
#     instructions=dedent("""
# 请严格遵循以下步骤和要求执行任务，确保输出的唯一性：

# #### 1. 输入解析

# * 分析输入的原始文本，识别其中包含的考试题目。
# * 每道题目通常由以下部分组成：
#     * **题干**：题目的主要内容。
#     * **选项**：A、B、C、D 等可供选择的答案。
#     * **答案**：明确指出正确答案的选项字母。

# #### 2. 题目类型识别

# * 根据以下标准判断每道题目的类型：
#     * **`"multi"` (多选题)**：
#         * 题干中可能包含“（可多选）”字样。
#         * 答案部分包含多个选项字母（例如：“A,B,C”）。
#     * **`"single"` (单选题)**：
#         * 答案部分仅包含一个选项字母。
#         * 选项通常不为“正确/错误”类型。
#     * **`"judge"` (判断题)**：
#         * 选项通常为“A: 正确”、“B: 错误”或类似的二元判断。

# #### 3. 数据提取与格式化

# * 针对每道题目，精确提取以下数据点并按照JSON结构化：
#     * **`"question"`**：提取完整的题干文本，不包含任何选项或答案信息。
#     * **`"answer"`**：
#         * 对于**多选题**，提取所有正确答案对应的选项字母，形成一个字符串数组（例如 `["A", "B", "C"]`）。
#         * 对于**单选题**，提取唯一的正确答案选项字母，形成一个包含单个字符串的数组（例如 `["B"]`）。
#         * 对于**判断题**，提取与题目正确判断（通常是“正确”）对应的选项字母，形成一个包含单个字符串的数组（例如 `["A"]`）。
#     * **`"type"`**：根据识别结果填入对应的题目类型字符串（`"multi"`、`"single"` 或 `"judge"`）。
#     * **`"options"`**：提取题目提供的所有选项。每个选项应包含其前面的字母和完整的选项文本，作为一个独立的字符串存储在数组中（例如 `["A: 选项文本", "B: 另一个选项文本"]`）。

# #### 4. 输出要求（最关键）

# * 你的最终回答**必须是一个能够直接被解析的JSON字符串**。
# * **禁止包含任何额外的文字、寒暄、解释、前导语、结束语或任何非JSON格式的内容。**
# * **示例输出格式：**
#     [
#         {
#             "question":"下列活动中哪些应当有操作规程并有过程与结果记录?（可多选）",
#             "answer":["A","B","C"],
#             "type":"multi",
#             "options":["A: 确认和验证","B: 设备的装配和校准","C: 厂房和设备的维护、清洁和消毒","D: 培训、更衣及卫生等"]
#         },
#         {
#             "question":"成品放行前是否可以发运?",
#             "answer":["B"],
#             "type":"single",
#             "options":["A: 可以","B: 不可以"]
#         },
#         {
#             "question":"自检完成后,是否需要形成自检报告?",
#             "answer":["A"],
#             "type":"judge",
#             "options":["A: 正确","B: 错误"]
#         }
#     ]
#     """),
# )

# test_extract_response = json_extract_agent.run(message=test_generate_result)
# try:
#     test_extract_result = json.loads(test_extract_response.content)
#     print("提取成功")
#     print(test_extract_result)
# except Exception:
#     print("提取失败")
#     print(test_extract_response)
