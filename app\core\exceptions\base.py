from enum import Enum
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass
import traceback
import uuid
from datetime import datetime


class ErrorCode(str, Enum):
    """标准错误码枚举"""
    
    # 成功
    SUCCESS = "00000"
    
    # 客户端错误 (4xxxx)
    BAD_REQUEST = "40000"
    UNAUTHORIZED = "40001"
    FORBIDDEN = "40003"
    NOT_FOUND = "40004"
    METHOD_NOT_ALLOWED = "40005"
    VALIDATION_ERROR = "40022"
    RATE_LIMIT_EXCEEDED = "40029"
    
    # 业务逻辑错误 (5xxxx)
    BUSINESS_ERROR = "50000"
    INTERNAL_SERVER_ERROR = "50001"
    DATABASE_ERROR = "50002"
    EXTERNAL_SERVICE_ERROR = "50003"
    CACHE_ERROR = "50004"
    
    # 认证和授权错误 (6xxxx)
    AUTHENTICATION_FAILED = "60001"
    TOKEN_EXPIRED = "60002"
    TOKEN_INVALID = "60003"
    PERMISSION_DENIED = "60004"
    
    # 资源相关错误 (7xxxx)
    RESOURCE_NOT_FOUND = "70001"
    RESOURCE_ALREADY_EXISTS = "70002"
    RESOURCE_CONFLICT = "70003"
    RESOURCE_LOCKED = "70004"
    
    # 网络和通信错误 (8xxxx)
    NETWORK_ERROR = "80001"
    TIMEOUT_ERROR = "80002"
    SERVICE_UNAVAILABLE = "80003"


class ErrorSeverity(str, Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    request_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    path: Optional[str] = None
    method: Optional[str] = None
    timestamp: Optional[datetime] = None
    additional_data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.additional_data is None:
            self.additional_data = {}


@dataclass
class ErrorDetail:
    """错误详情"""
    field: Optional[str] = None
    message: str = ""
    code: Optional[str] = None
    value: Optional[Any] = None


class BaseException(Exception):
    """基础异常类"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR,
        http_status: int = 500,
        details: Optional[List[ErrorDetail]] = None,
        context: Optional[ErrorContext] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        should_log: bool = True,
        user_message: Optional[str] = None,
        **kwargs
    ):
        self.message = message
        self.code = code
        self.http_status = http_status
        self.details = details or []
        self.context = context
        self.severity = severity
        self.should_log = should_log
        self.user_message = user_message or message
        self.extra_data = kwargs
        self.traceback = traceback.format_exc()
        
        # 生成唯一错误ID
        self.error_id = str(uuid.uuid4())
        
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "error_id": self.error_id,
            "code": self.code.value,
            "message": self.user_message,
            "details": [
                {
                    "field": d.field,
                    "message": d.message,
                    "code": d.code,
                    "value": d.value
                } for d in self.details
            ],
            "severity": self.severity.value,
            "timestamp": self.context.timestamp.isoformat() if self.context else None,
            "request_id": self.context.request_id if self.context else None,
        }
    
    def add_detail(self, field: str, message: str, code: Optional[str] = None, value: Optional[Any] = None):
        """添加错误详情"""
        detail = ErrorDetail(field=field, message=message, code=code, value=value)
        self.details.append(detail)
        return self
    
    def set_context(self, context: ErrorContext):
        """设置错误上下文"""
        self.context = context
        return self