from typing import Optional

import jwt
from fastapi import Request
from redis import Redis

from app.core.config import settings
from app.core.exceptions import AuthenticationException


async def verify_token_from_request(
    request: Request = None, redis_client: Redis = None
):
    # 如果 request is None, 也就是Socket连接，其中有自己单独的鉴权方法直接返回
    if request is None:
        return

    # 获取当前请求的URI
    uri = request.url.path

    # 如果URI中包含'no-auth'或'socket'，则跳过验证
    if "no-auth" in uri:
        return

    # 如果没有提供redis_client，则从依赖注入获取
    if redis_client is None:
        from app.api.deps import get_redis_client

        redis_client = get_redis_client()

    # 从请求头中获取Access-Token
    access_token = request.headers.get("Access-Token")
    return await verify_token(access_token, redis_client)


async def verify_token(access_token: str, redis_client=None) -> Optional[str]:
    if not access_token:
        raise AuthenticationException("Access-Token is missing in headers")

    # 如果没有提供redis连接，从依赖注入获取
    if redis_client is None:
        from app.api.deps import get_redis_client

        redis_client = get_redis_client()

    # 从 Redis 中获取 Access-Token 对应的 JWT 值
    token_jwt_key = "YshBase:token_" + access_token
    token_jwt_value = redis_client.get(token_jwt_key)
    if not token_jwt_value:
        raise AuthenticationException("Access-Token is invalid")

    try:
        actual_jwt = token_jwt_value.decode("utf-8")
        # 解析 JWT 值，获取存储在其中的用户 ID

        actual_jwt = actual_jwt.replace('"', "")
        payload = jwt.decode(actual_jwt, options={"verify_signature": False})

        user_id = payload.get("id")
        if not user_id:
            raise AuthenticationException("Invalid token payload: missing user_id")

        return user_id

    except jwt.DecodeError as e:
        raise AuthenticationException(f"Invalid token format: {str(e)}")
    except Exception as e:
        raise AuthenticationException(f"Token verification failed: {str(e)}")
