import json
import uuid

from docx.opc.exceptions import PackageNotFoundError
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from redis import Redis
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.api.deps import get_mysql_session, get_redis_client
from app.core.exceptions import ResourceNotFoundException, ValidationException
from app.core.logging.log_config import get_logger
from app.shared.schemas.test_generate import (
    TaskSubmissionResponse,
    TestGenerateTaskCreate,
)
from app.domain.knowledge_base.services.test_generate_service import TestGenerateService
from app.shared.utils.docx_tools import docx_to_html

router = APIRouter()
logger = get_logger("app.api.test_generate")


@router.post(
    "/submit_task",
    summary="提交考题生成任务",
    description="提交文档内容并获取任务ID，用于后续流式获取考题",
)
async def submit_test_generation_task(
    document_input: TestGenerateTask<PERSON>reate,
    redis_client: Redis = Depends(get_redis_client),
    mysql_session: Session = Depends(get_mysql_session),
) -> TaskSubmissionResponse:
    """提交考题生成任务接口

    Args:
        document_input: 包含文档内容的请求体
        redis_client: Redis客户端依赖
        mysql_session: MySQL数据库会话依赖

    Returns:
        TaskSubmissionResponse: 包含任务ID的响应
    """
    if document_input.source_type not in ["BY_INPUT", "BY_SUBJECT"]:
        raise ValidationException(msg="source_type必须是BY_INPUT或BY_SUBJECT")
    if (
        document_input.source_type == "BY_INPUT"
        and not document_input.document_content.strip()
    ):
        raise ValidationException(msg="文档内容不能为空")
    if document_input.source_type == "BY_SUBJECT" and not document_input.subject_id:
        raise ValidationException(msg="subject_id不能为空")

    if document_input.source_type == "BY_SUBJECT":
        # 从数据库中查询subject_id对应的文档内容
        statement = text(
            "SELECT associated_document_id,associated_document_type FROM ysh_tms.training_subject WHERE id = :id"
        ).bindparams(id=document_input.subject_id)
        result = mysql_session.execute(statement).fetchone()

        if not result:
            raise ResourceNotFoundException(msg="目标课题不存在")

        document_id = result[0]
        statement = text(
            "SELECT save_path FROM ysh_dms.doc_file WHERE doc_id = :id AND file_type = 0"
        ).bindparams(id=document_id)
        result = mysql_session.execute(statement).fetchone()
        logger.debug(f"查询到 save_path = {result[0]}")
        if not result:
            raise ResourceNotFoundException(msg="目标文档不存在")

        # 根据 save_path 提取文件内容,save_path存储的文件内容为 docx 格式
        try:
            document_content = docx_to_html(result[0])
            logger.debug(
                f"从路径 {result[0]} 提取到文档内容,{document_content[0:50]}..."
            )
        except PackageNotFoundError:
            raise ResourceNotFoundException(msg="目标文档不存在")
    if document_input.source_type == "BY_INPUT":
        document_content = document_input.document_content

    task_id = str(uuid.uuid4())
    try:
        # Store the document content in Redis with an expiration time (e.g., 1 hour)
        redis_client.set(
            f"tms:test_generate:task:{task_id}",
            json.dumps(
                {
                    "document_content": document_content,
                    "knowledge_base_id": document_input.knowledge_base_id,
                    "document_ids": document_input.document_ids,
                }
            ),
            ex=3600,
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"无法连接到Redis或存储任务失败: {str(e)}"
        )

    return TaskSubmissionResponse(task_id=task_id, msg="任务已成功提交")


@router.get(
    "/stream/{task_id}",  # Modified path to include task_id
    summary="根据任务ID流式生成考题",
    description="根据提交的任务ID，基于文档内容流式生成GMP培训考题",
)
async def generate_test_questions_stream(
    task_id: str,
    redis_client: Redis = Depends(get_redis_client),
) -> StreamingResponse:
    """流式生成考题接口

    Args:
        task_id: 任务ID
        redis_client: Redis客户端依赖

    Returns:
        StreamingResponse: SSE流式响应
    """
    document_content_bytes = redis_client.get(f"tms:test_generate:task:{task_id}")
    if not document_content_bytes:
        raise HTTPException(status_code=404, detail=f"任务ID {task_id} 未找到或已过期")

    document_content = document_content_bytes.decode("utf-8")

    if not document_content.strip():
        # This case should ideally be caught by the POST endpoint or if Redis returns empty string
        raise HTTPException(status_code=400, detail="任务关联的文档内容为空")

    service = TestGenerateService()

    async def event_stream():
        """SSE事件流生成器"""
        try:
            async for event_data in service.generate_test_questions_stream(
                json.loads(document_content)
            ):
                # 格式化SSE数据
                event_type = event_data.get("event", "message")
                data = event_data.get("data", {})

                # 发送SSE格式的数据
                yield f"event: {event_type}\n"
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

                # 如果是错误或成功事件，结束流
                if event_type in ["error", "success"]:
                    break

        except Exception as e:
            # 发送错误事件
            error_data = {
                "message": "服务器内部错误",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00",
            }
            yield "event: error\n"
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        },
    )
