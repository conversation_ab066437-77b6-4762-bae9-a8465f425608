# 应用配置
DEBUG=True
HOST=0.0.0.0
PORT=8000
WORKERS=1
PROJECT_NAME="AI BioHub"
PROJECT_DESCRIPTION="高级对话智能体系统，支持对话记忆、多模型切换、多模态交互和搜索工具集成"
VERSION="0.1.0"
API_V1_STR="/api/v1"
SECRET_KEY="your-secret-key-here"
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# 第三方认证服务
AUTH_SERVICE_URL=http://auth-service:8080/api

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=root
POSTGRES_PASSWORD=821206
POSTGRES_DB=ai_biohub
POSTGRES_PORT=5432
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_SERVER}:${POSTGRES_PORT}/${POSTGRES_DB}

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
CLAUDE_API_KEY=your-claude-api-key
GEMINI_API_KEY=your-gemini-api-key
DASHSCOPE_API_KEY=your-dashscope-api-key

# 存储配置
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=50000000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log