from typing import Generator, Optional, Any
from contextlib import contextmanager
from sqlalchemy.orm import Session

from app.infrastructure.database.session import get_pg_session, get_mysql_session
from app.core.exceptions import DatabaseException


class DatabaseDependencies:
    """数据库依赖注入"""
    
    @staticmethod
    def get_pg_session() -> Generator[Session, None, None]:
        """获取PostgreSQL会话依赖"""
        session = get_pg_session()
        try:
            yield session
        finally:
            session.close()
    
    @staticmethod
    def get_mysql_session() -> Generator[Session, None, None]:
        """获取MySQL会话依赖"""
        session = get_mysql_session()
        try:
            yield session
        finally:
            session.close()
    
    @staticmethod
    @contextmanager
    def get_transactional_session(database: str = "postgres") -> Generator[Session, None, None]:
        """获取事务性会话"""
        session_factory = get_pg_session if database == "postgres" else get_mysql_session
        session = session_factory()
        
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise DatabaseException(f"事务执行失败: {str(e)}") from e
        finally:
            session.close()


# 创建依赖注入实例
database_deps = DatabaseDependencies()

# 便利函数
def get_session(database: str = "postgres") -> Session:
    """获取数据库会话"""
    if database == "postgres":
        return get_pg_session()
    else:
        return get_mysql_session()

def get_transactional_session(database: str = "postgres"):
    """获取事务性会话上下文管理器"""
    return database_deps.get_transactional_session(database)

# FastAPI 依赖
def get_db_session(database: str = "postgres"):
    """FastAPI数据库会话依赖"""
    if database == "postgres":
        return database_deps.get_pg_session()
    else:
        return database_deps.get_mysql_session()