import docx
from docx.oxml.ns import qn


def docx_to_html(docx_path):
    """
    将docx文件转换为HTML格式，保留段落和表格结构

    参数:
    docx_path (str): docx文件路径

    返回:
    str: HTML格式的内容
    """
    doc = docx.Document(docx_path)
    html_content = []
    html_content.append("<html><body>")

    for element in doc.element.body:
        if element.tag.endswith("p"):  # 处理段落
            p = docx.text.paragraph.Paragraph(element, doc)
            # 处理段落文本
            text = "".join(r.text for r in p.runs)
            if text.strip():  # 忽略空段落
                html_content.append(f"<p>{text}</p>")

        elif element.tag.endswith("tbl"):  # 处理表格
            html_content.append("<table>")
            table = docx.table.Table(element, doc)
            # 追踪合并单元格的占用情况
            grid = {}

            for row_idx, row in enumerate(table.rows):
                html_content.append("<tr>")
                for col_idx, cell in enumerate(row.cells):
                    # 检查当前单元格是否被上方的合并单元格占用
                    if (row_idx, col_idx) in grid:
                        continue

                    # 获取单元格的合并属性
                    grid_span = get_cell_grid_span(cell)
                    v_merge = get_cell_v_merge(cell)

                    # 计算单元格的结束行
                    rowspan = 1
                    if v_merge == "restart":
                        rowspan = calculate_rowspan(table, row_idx, col_idx)
                        # 标记被合并的单元格位置
                        for rs in range(1, rowspan):
                            grid[(row_idx + rs, col_idx)] = True

                    # 处理单元格中的所有段落
                    cell_content = []
                    for paragraph in cell.paragraphs:
                        para_text = "".join(r.text for r in paragraph.runs)
                        if para_text.strip():
                            cell_content.append(para_text)

                    # 单元格内容用<br>连接多个段落
                    cell_html = "<br>".join(cell_content)

                    # 添加合并属性到HTML标签
                    td_attrs = []
                    if grid_span > 1:
                        td_attrs.append(f'colspan="{grid_span}"')
                    if rowspan > 1:
                        td_attrs.append(f'rowspan="{rowspan}"')

                    td_attr_str = " " + " ".join(td_attrs) if td_attrs else ""
                    html_content.append(f"<td{td_attr_str}>{cell_html}</td>")

                html_content.append("</tr>")

            html_content.append("</table>")

    html_content.append("</body></html>")
    return "".join(html_content)


def get_cell_grid_span(cell):
    """获取单元格的水平合并数"""
    tc = cell._tc
    grid_span = tc.xpath(".//w:gridSpan")
    if grid_span:
        return int(grid_span[0].attrib.get(qn("w:val"), 1))
    return 1


def get_cell_v_merge(cell):
    """获取单元格的垂直合并状态"""
    tc = cell._tc
    v_merge = tc.xpath(".//w:vMerge")
    if v_merge:
        return v_merge[0].attrib.get(qn("w:val"), "restart")
    return None


def calculate_rowspan(table, start_row, col_idx):
    """计算从起始行开始的垂直合并单元格的行数"""
    rowspan = 1
    max_rows = len(table.rows)

    for i in range(start_row + 1, max_rows):
        cell = table.cell(i, col_idx)
        v_merge = get_cell_v_merge(cell)
        if v_merge == "continue":
            rowspan += 1
        else:
            break

    return rowspan


# 使用示例
if __name__ == "__main__":
    try:
        html_output = docx_to_html("example.docx")
        with open("output.html", "w", encoding="utf-8") as f:
            f.write(html_output)
        print("转换完成，已保存为output.html")
    except Exception as e:
        print(f"转换失败: {e}")
