import os
from typing import Dict, Any, Optional
from urllib.parse import quote_plus
from pydantic import field_validator, computed_field

from app.core.config.base import BaseConfig


class DatabaseConfig(BaseConfig):
    """数据库配置"""
    
    # PostgreSQL配置
    POSTGRES_SERVER: str = os.getenv("DATABASE__POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("DATABASE__POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("DATABASE__POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("DATABASE__POSTGRES_DB", "biohub_dev")
    POSTGRES_PORT: int = int(os.getenv("DATABASE__POSTGRES_PORT", "5432"))
    
    # MySQL配置
    MYSQL_SERVER: str = os.getenv("DATABASE__MYSQL_SERVER", "localhost")
    MYSQL_USER: str = os.getenv("DATABASE__MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.getenv("DATABASE__MYSQL_PASSWORD", "root")
    MYSQL_DB: str = os.getenv("DATABASE__MYSQL_DB", "biohub_dev")
    MYSQL_PORT: int = int(os.getenv("DATABASE__MYSQL_PORT", "3306"))
    
    # 连接池配置
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    
    @computed_field
    @property
    def postgres_url(self) -> str:
        """PostgreSQL连接URL"""
        return f"postgresql://{quote_plus(self.POSTGRES_USER)}:{quote_plus(self.POSTGRES_PASSWORD)}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    @computed_field
    @property
    def mysql_url(self) -> str:
        """MySQL连接URL"""
        return f"mysql+pymysql://{quote_plus(self.MYSQL_USER)}:{quote_plus(self.MYSQL_PASSWORD)}@{self.MYSQL_SERVER}:{self.MYSQL_PORT}/{self.MYSQL_DB}"
    
    @field_validator("POSTGRES_PORT", "MYSQL_PORT")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
    
    @field_validator("DB_POOL_SIZE")
    def validate_pool_size(cls, v):
        if v < 1:
            raise ValueError("Pool size must be at least 1")
        return v
    
    def validate_config(self) -> None:
        """验证配置"""
        # 验证必需的配置项
        required_fields = [
            "POSTGRES_SERVER", "POSTGRES_USER", "POSTGRES_PASSWORD", "POSTGRES_DB",
            "MYSQL_SERVER", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DB"
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                raise ValueError(f"Database configuration field {field} is required")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息（不包含敏感信息）"""
        return {
            "postgres_server": self.POSTGRES_SERVER,
            "postgres_port": self.POSTGRES_PORT,
            "postgres_db": self.POSTGRES_DB,
            "mysql_server": self.MYSQL_SERVER,
            "mysql_port": self.MYSQL_PORT,
            "mysql_db": self.MYSQL_DB,
            "pool_size": self.DB_POOL_SIZE,
            "max_overflow": self.DB_MAX_OVERFLOW,
            "pool_timeout": self.DB_POOL_TIMEOUT,
            "pool_recycle": self.DB_POOL_RECYCLE,
        }