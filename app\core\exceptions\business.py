from typing import Any, Dict, List, Optional
from fastapi import status

from app.core.exceptions.base import BaseException, ErrorCode, ErrorSeverity, ErrorDetail


class ValidationException(BaseException):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str = "数据验证失败",
        field_errors: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        details = []
        if field_errors:
            for field, error_msg in field_errors.items():
                details.append(ErrorDetail(field=field, message=error_msg))
        
        super().__init__(
            message=message,
            code=ErrorCode.VALIDATION_ERROR,
            http_status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
            severity=ErrorSeverity.LOW,
            **kwargs
        )


class AuthenticationException(BaseException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.AUTHENTICATION_FAILED,
            http_status=status.HTTP_401_UNAUTHORIZED,
            severity=ErrorSeverity.MEDIUM,
            user_message="请先登录",
            **kwargs
        )


class AuthorizationException(BaseException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.PERMISSION_DENIED,
            http_status=status.HTTP_403_FORBIDDEN,
            severity=ErrorSeverity.MEDIUM,
            user_message="您没有权限执行此操作",
            **kwargs
        )


class ResourceNotFoundException(BaseException):
    """资源未找到异常"""
    
    def __init__(self, message: str = "资源不存在", resource_type: Optional[str] = None, **kwargs):
        user_message = f"{resource_type}不存在" if resource_type else "资源不存在"
        
        super().__init__(
            message=message,
            code=ErrorCode.RESOURCE_NOT_FOUND,
            http_status=status.HTTP_404_NOT_FOUND,
            severity=ErrorSeverity.LOW,
            user_message=user_message,
            **kwargs
        )


class ResourceConflictException(BaseException):
    """资源冲突异常"""
    
    def __init__(self, message: str = "资源冲突", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.RESOURCE_CONFLICT,
            http_status=status.HTTP_409_CONFLICT,
            severity=ErrorSeverity.MEDIUM,
            user_message="资源已存在或发生冲突",
            **kwargs
        )


class BusinessException(BaseException):
    """业务逻辑异常"""
    
    def __init__(self, message: str = "业务逻辑错误", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.BUSINESS_ERROR,
            http_status=status.HTTP_400_BAD_REQUEST,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class DatabaseException(BaseException):
    """数据库异常"""
    
    def __init__(self, message: str = "数据库操作失败", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.DATABASE_ERROR,
            http_status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            severity=ErrorSeverity.HIGH,
            user_message="数据库操作失败，请稍后重试",
            **kwargs
        )


class ExternalServiceException(BaseException):
    """外部服务异常"""
    
    def __init__(self, message: str = "外部服务调用失败", service_name: Optional[str] = None, **kwargs):
        user_message = f"{service_name}服务暂时不可用" if service_name else "外部服务暂时不可用"
        
        super().__init__(
            message=message,
            code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            http_status=status.HTTP_503_SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.HIGH,
            user_message=user_message,
            **kwargs
        )


class RateLimitException(BaseException):
    """限流异常"""
    
    def __init__(self, message: str = "请求过于频繁", retry_after: Optional[int] = None, **kwargs):
        user_message = f"请求过于频繁，请{retry_after}秒后重试" if retry_after else "请求过于频繁，请稍后重试"
        
        super().__init__(
            message=message,
            code=ErrorCode.RATE_LIMIT_EXCEEDED,
            http_status=status.HTTP_429_TOO_MANY_REQUESTS,
            severity=ErrorSeverity.LOW,
            user_message=user_message,
            **kwargs
        )


class CacheException(BaseException):
    """缓存异常"""
    
    def __init__(self, message: str = "缓存操作失败", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.CACHE_ERROR,
            http_status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            severity=ErrorSeverity.MEDIUM,
            user_message="缓存服务异常",
            should_log=True,
            **kwargs
        )


class NetworkException(BaseException):
    """网络异常"""
    
    def __init__(self, message: str = "网络连接失败", **kwargs):
        super().__init__(
            message=message,
            code=ErrorCode.NETWORK_ERROR,
            http_status=status.HTTP_503_SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.HIGH,
            user_message="网络连接失败，请检查网络连接",
            **kwargs
        )


class TimeoutException(BaseException):
    """超时异常"""
    
    def __init__(self, message: str = "请求超时", timeout: Optional[int] = None, **kwargs):
        user_message = f"请求超时({timeout}秒)，请稍后重试" if timeout else "请求超时，请稍后重试"
        
        super().__init__(
            message=message,
            code=ErrorCode.TIMEOUT_ERROR,
            http_status=status.HTTP_504_GATEWAY_TIMEOUT,
            severity=ErrorSeverity.MEDIUM,
            user_message=user_message,
            **kwargs
        )