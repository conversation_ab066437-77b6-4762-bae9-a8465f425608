from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.logging.log_utils import log_database_operation
from app.core.logging.log_config import get_logger
from app.infrastructure.database.session import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """CRUD操作的基础类"""

    def __init__(self, model: Type[ModelType]):
        """初始化"""
        self.model = model
        self.logger = get_logger(f"app.crud.{model.__name__.lower()}")

    @log_database_operation("查询")
    async def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """获取单个对象"""
        self.logger.debug(f"查询{self.model.__name__}，ID: {id}")
        result = db.query(self.model).filter(self.model.id == id).first()
        if result:
            self.logger.debug(f"成功查询到{self.model.__name__}，ID: {id}")
        else:
            self.logger.debug(f"未找到{self.model.__name__}，ID: {id}")
        return result

    @log_database_operation("批量查询")
    async def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个对象"""
        self.logger.debug(
            f"批量查询{self.model.__name__}，skip: {skip}, limit: {limit}"
        )
        results = db.query(self.model).offset(skip).limit(limit).all()
        self.logger.debug(
            f"批量查询{self.model.__name__}完成，返回 {len(results)} 条记录"
        )
        return results

    @log_database_operation("创建")
    async def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        self.logger.debug(f"创建{self.model.__name__}")
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        self.logger.info(f"成功创建{self.model.__name__}，ID: {db_obj.id}")
        return db_obj

    @log_database_operation("更新")
    async def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """更新对象"""
        self.logger.debug(f"更新{self.model.__name__}，ID: {db_obj.id}")
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        self.logger.info(f"成功更新{self.model.__name__}，ID: {db_obj.id}")
        return db_obj

    @log_database_operation("删除")
    async def remove(self, db: Session, *, id: int) -> ModelType:
        """删除对象"""
        self.logger.debug(f"删除{self.model.__name__}，ID: {id}")
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
            self.logger.info(f"成功删除{self.model.__name__}，ID: {id}")
        else:
            self.logger.warning(f"尝试删除不存在的{self.model.__name__}，ID: {id}")
        return obj
