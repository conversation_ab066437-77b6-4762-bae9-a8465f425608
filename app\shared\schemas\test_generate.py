from typing import Optional, List

from pydantic import BaseModel


class TestGenerateTaskCreate(BaseModel):
    """Create model for test generate task"""

    source_type: str
    document_content: str
    subject_id: int
    knowledge_base_id: int
    document_ids: Optional[List[int]] # 假设 document_ids 是整数列表


class TaskSubmissionResponse(BaseModel):
    """Response model for task submission"""

    task_id: str
    message: str


class TestGenerateResponse(BaseModel):
    """Test generate response model"""

    success: bool
    message: str
    questions: Optional[List[dict]] = None # 假设 questions 是字典列表
    count: Optional[int] = None
    error: Optional[str] = None
    timestamp: str