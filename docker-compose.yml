version: '3.8'

services:
  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - APP__ENVIRONMENT=development
      - APP__DEBUG=true
      - DATABASE__POSTGRES_SERVER=postgres
      - DATABASE__POSTGRES_USER=postgres
      - DATABASE__POSTGRES_PASSWORD=postgres
      - DATABASE__POSTGRES_DB=biohub_dev
      - DATABASE__POSTGRES_PORT=5432
      - DATABASE__MYSQL_SERVER=mysql
      - DATABASE__MYSQL_USER=root
      - DATABASE__MYSQL_PASSWORD=mysql
      - DATABASE__MYSQL_DB=biohub_dev
      - DATABASE__MYSQL_PORT=3306
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - DASHSCOPE_API_KEY=your-api-key-here
    depends_on:
      - postgres
      - mysql
      - redis
      - elasticsearch
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./static:/app/static
    networks:
      - biohub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=biohub_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - biohub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=mysql
      - MYSQL_DATABASE=biohub_dev
      - MYSQL_USER=biohub
      - MYSQL_PASSWORD=biohub
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - biohub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - biohub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - biohub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana (可选)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - biohub-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./static:/var/www/static
      - ./docker/ssl:/etc/ssl/certs
    depends_on:
      - app
    networks:
      - biohub-network
    restart: unless-stopped

volumes:
  postgres_data:
  mysql_data:
  redis_data:
  elasticsearch_data:

networks:
  biohub-network:
    driver: bridge