from typing import Generic, Optional, TypeVar

from pydantic import BaseModel, Field

# 定义泛型类型变量
DataT = TypeVar("DataT")


class BaseResponse(BaseModel, Generic[DataT]):
    """通用API响应模型 - 统一响应结构"""
    
    # 按照目标结构调整字段顺序：code, msg, success, data
    code: int = Field(default=1, description="响应代码")
    msg: str = Field(default="操作成功", description="响应消息")
    success: bool = Field(default=True, description="请求是否成功")
    data: Optional[DataT] = Field(default=None, description="响应数据")

    class Config:
        json_schema_extra = {
            "example": {
                "code": 1,
                "msg": "操作成功",
                "success": True,
                "data": {},
            }
        }


class SuccessResponse(BaseResponse[DataT]):
    """成功响应模型"""

    def __init__(self, data: DataT = None, msg: str = "操作成功", code: int = 1, **kwargs):
        super().__init__(
            code=code,
            msg=msg,
            success=True,
            data=data,
            **kwargs,
        )


class ErrorResponse(BaseResponse[None]):
    """错误响应模型"""

    def __init__(self, msg: str, code: int = 0, **kwargs):
        super().__init__(
            code=code,
            msg=msg,
            success=False,
            data=None,
            **kwargs,
        )


class PaginationMeta(BaseModel):
    """分页元数据"""

    total: int = Field(description="总记录数")
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页大小")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")


class PaginatedData(BaseModel, Generic[DataT]):
    """分页数据模型"""

    items: list[DataT] = Field(description="数据列表")
    meta: PaginationMeta = Field(description="分页元数据")


class PaginatedResponse(BaseResponse[PaginatedData[DataT]]):
    """分页响应模型"""

    def __init__(
        self,
        items: list[DataT],
        total: int,
        page: int,
        page_size: int,
        msg: str = "获取成功",
        code: int = 1,
        **kwargs,
    ):
        total_pages = (total + page_size - 1) // page_size
        has_next = page < total_pages
        has_prev = page > 1

        meta = PaginationMeta(
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev,
        )

        paginated_data = PaginatedData(items=items, meta=meta)

        super().__init__(
            code=code,
            msg=msg,
            success=True,
            data=paginated_data,
            **kwargs,
        )
