from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.pool import StaticPool
from sqlalchemy.sql import func
from datetime import datetime
import os

from app.core.config.settings import settings
from app.core.exceptions import DatabaseException

# 创建基础模型类
Base = declarative_base()

# 数据库连接配置
class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        self.postgres_url = settings.database.postgres_url
        self.mysql_url = settings.database.mysql_url
        self.pool_size = settings.database.DB_POOL_SIZE
        self.max_overflow = settings.database.DB_MAX_OVERFLOW
        self.pool_timeout = settings.database.DB_POOL_TIMEOUT
        self.pool_recycle = settings.database.DB_POOL_RECYCLE
        self.echo = settings.app.DEBUG

# 数据库引擎管理器
class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self._pg_engine = None
        self._mysql_engine = None
        self._pg_session_factory = None
        self._mysql_session_factory = None
    
    @property
    def pg_engine(self):
        """PostgreSQL引擎"""
        if self._pg_engine is None:
            self._pg_engine = create_engine(
                self.config.postgres_url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                pool_pre_ping=True,
                echo=self.config.echo,
            )
        return self._pg_engine
    
    @property
    def mysql_engine(self):
        """MySQL引擎"""
        if self._mysql_engine is None:
            self._mysql_engine = create_engine(
                self.config.mysql_url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                pool_pre_ping=True,
                echo=self.config.echo,
            )
        return self._mysql_engine
    
    @property
    def pg_session_factory(self):
        """PostgreSQL会话工厂"""
        if self._pg_session_factory is None:
            self._pg_session_factory = sessionmaker(
                bind=self.pg_engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
        return self._pg_session_factory
    
    @property
    def mysql_session_factory(self):
        """MySQL会话工厂"""
        if self._mysql_session_factory is None:
            self._mysql_session_factory = sessionmaker(
                bind=self.mysql_engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
        return self._mysql_session_factory
    
    def get_pg_session(self) -> Session:
        """获取PostgreSQL会话"""
        return self.pg_session_factory()
    
    def get_mysql_session(self) -> Session:
        """获取MySQL会话"""
        return self.mysql_session_factory()
    
    def init_databases(self):
        """初始化数据库"""
        try:
            # 创建PostgreSQL表
            Base.metadata.create_all(bind=self.pg_engine)
            
            # 创建MySQL表
            Base.metadata.create_all(bind=self.mysql_engine)
            
            print("数据库初始化完成")
        except Exception as e:
            raise DatabaseException(f"数据库初始化失败: {str(e)}") from e
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            "postgres": {"status": "unknown", "error": None},
            "mysql": {"status": "unknown", "error": None},
        }
        
        # 检查PostgreSQL
        try:
            with self.get_pg_session() as session:
                session.execute("SELECT 1")
                health["postgres"]["status"] = "healthy"
        except Exception as e:
            health["postgres"]["status"] = "unhealthy"
            health["postgres"]["error"] = str(e)
        
        # 检查MySQL
        try:
            with self.get_mysql_session() as session:
                session.execute("SELECT 1")
                health["mysql"]["status"] = "healthy"
        except Exception as e:
            health["mysql"]["status"] = "unhealthy"
            health["mysql"]["error"] = str(e)
        
        return health
    
    def close_all(self):
        """关闭所有连接"""
        if self._pg_engine:
            self._pg_engine.dispose()
        if self._mysql_engine:
            self._mysql_engine.dispose()

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 便利函数
def get_pg_session() -> Session:
    """获取PostgreSQL会话"""
    return db_manager.get_pg_session()

def get_mysql_session() -> Session:
    """获取MySQL会话"""
    return db_manager.get_mysql_session()

def init_db():
    """初始化数据库"""
    return db_manager.init_databases()

def health_check():
    """数据库健康检查"""
    return db_manager.health_check()

# 向后兼容
pg_engine = db_manager.pg_engine
mysql_engine = db_manager.mysql_engine
PgSessionLocal = db_manager.pg_session_factory
MySQLSessionLocal = db_manager.mysql_session_factory
SessionLocal = PgSessionLocal  # 默认使用PostgreSQL

# FastAPI依赖函数
def get_pg_db():
    """获取PostgreSQL数据库会话的依赖函数"""
    db = get_pg_session()
    try:
        yield db
    finally:
        db.close()

def get_mysql_db():
    """获取MySQL数据库会话的依赖函数"""
    db = get_mysql_session()
    try:
        yield db
    finally:
        db.close()
