from dataclasses import dataclass
from logging import getLogger
from typing import Any, Dict, List, Optional

from agno.embedder import Embedder
from typing_extensions import Literal

logger = getLogger(__name__)

try:
    from openai import OpenAI
    from openai.types.create_embedding_response import CreateEmbeddingResponse
except ImportError:
    raise ImportError("`openai` not installed")


@dataclass
class BailianEmbeddings(Embedder):
    id: str = "text-embedding-v3"
    dimensions: int = 1024
    api_key: str = "your_api_key"
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    encoding_format: Literal["float", "base64"] = "float"

    openai_client: Optional[OpenAI] = None

    @property
    def client(self) -> OpenAI:
        if self.openai_client:
            return self.openai_client

        _client_params: Dict[str, Any] = {
            "api_key": self.api_key,
            "base_url": self.base_url,
        }
        client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )
        self.openai_client = client
        return self.openai_client

    def response(self, text: str) -> CreateEmbeddingResponse:
        _request_params: Dict[str, Any] = {
            "input": text,
            "model": self.id,
            "encoding_format": self.encoding_format,
        }
        match self.id:
            case "text-embedding-v3":
                if self.dimensions not in [1024, 768, 512, 256, 128, 64]:
                    raise ValueError(
                        f"Dimensions must be one of [1024, 768, 512, 256, 128, 64], got {self.dimensions}"
                    )
                _request_params["dimensions"] = self.dimensions
            case "text-embedding-v2", "text-embedding-v1":
                _request_params["dimensions"] = 1024
            case _:
                raise ValueError(f"Unknown model: {self.id}")

        return self.client.embeddings.create(**_request_params)

    def get_embedding(self, text: str) -> List[float]:
        response: CreateEmbeddingResponse = self.response(text=text)
        try:
            return response.data[0].embedding
        except Exception as e:
            logger.warning(e)
            return []


if __name__ == "__main__":
    embeddings = BailianEmbeddings(api_key="SK-1234567890")
    print(embeddings.get_embedding("Hello world"))
