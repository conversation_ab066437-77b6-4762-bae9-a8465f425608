from fastapi import APIRouter
from app.api.versioning import APIVersion
from app.api.base import create_versioned_router

# V1 API路由器
v1_router = create_versioned_router(
    version=APIVersion.V1,
    prefix="",
    tags=["V1 API"]
)

# V2 API路由器（测试版）
v2_router = create_versioned_router(
    version=APIVersion.V2,
    prefix="",
    tags=["V2 API", "Beta"]
)

# 导入V1端点
from app.api.v1.endpoints.knownledge_base import router as v1_knowledge_base_router
from app.api.v1.endpoints.example import router as v1_example_router
from app.api.v1.endpoints.test_generate import router as v1_test_generate_router

# 注册V1路由
v1_router.include_router(v1_knowledge_base_router, prefix="/knowledge_base", tags=["知识库"])
v1_router.include_router(v1_example_router, prefix="/example", tags=["示例API"])
v1_router.include_router(v1_test_generate_router, prefix="/test-generate", tags=["考题生成"])

# 创建主API路由器
api_router = APIRouter()
api_router.include_router(v1_router)
api_router.include_router(v2_router)

# 添加版本信息端点
@api_router.get("/versions", tags=["API 信息"])
async def get_api_versions():
    """获取所有API版本信息"""
    from app.api.versioning import api_registry
    
    versions = []
    for version in api_registry.get_all_versions():
        version_info = api_registry.get_version_info(version)
        if version_info:
            versions.append({
                "version": version,
                "status": version_info.status,
                "description": version_info.description,
                "tags": version_info.tags,
                "deprecated_in": version_info.deprecated_in,
                "removed_in": version_info.removed_in,
                "migration_guide": version_info.migration_guide,
            })
    
    return {
        "versions": versions,
        "current": APIVersion.V1,
        "latest": APIVersion.V1,
    }

# 添加健康检查端点
@api_router.get("/health", tags=["系统"])
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": APIVersion.V1,
        "timestamp": "2023-01-01T00:00:00Z"
    }