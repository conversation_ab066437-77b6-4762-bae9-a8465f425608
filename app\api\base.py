from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse

from app.api.versioning import APIVersion, APIStatus, api_registry
from app.shared.utils.response_utils import error_response


class VersionedAPIRouter(APIRouter):
    """版本化的API路由器"""
    
    def __init__(
        self,
        version: APIVersion,
        *,
        prefix: str = "",
        tags: Optional[List[str]] = None,
        dependencies: Optional[List] = None,
        responses: Optional[Dict[int, Dict[str, Any]]] = None,
        deprecated: bool = False,
        include_in_schema: bool = True,
        **kwargs
    ):
        # 构建版本化的前缀
        version_prefix = f"/{version.value}"
        full_prefix = f"{version_prefix}{prefix}"
        
        # 添加版本标签
        version_tags = tags or []
        version_tags.append(f"API {version.value}")
        
        super().__init__(
            prefix=full_prefix,
            tags=version_tags,
            dependencies=dependencies,
            responses=responses,
            deprecated=deprecated,
            include_in_schema=include_in_schema,
            **kwargs
        )
        
        self.version = version
        self.deprecated = deprecated
        
        # 注意：版本检查中间件需要在主应用程序级别添加
        # 而不是在路由器级别
    
    def add_route(self, path: str, endpoint, **kwargs):
        """添加路由时进行版本检查"""
        version_info = api_registry.get_version_info(self.version)
        
        if version_info and version_info.status == APIStatus.DEPRECATED:
            # 为弃用的API添加警告头
            original_endpoint = endpoint
            
            def deprecated_endpoint(*args, **kwargs):
                response = original_endpoint(*args, **kwargs)
                if hasattr(response, 'headers'):
                    response.headers["X-API-Deprecated"] = "true"
                    response.headers["X-API-Version"] = self.version.value
                    if version_info.migration_guide:
                        response.headers["X-API-Migration-Guide"] = version_info.migration_guide
                return response
            
            endpoint = deprecated_endpoint
        
        super().add_route(path, endpoint, **kwargs)


class VersionCheckMiddleware:
    """版本检查中间件"""
    
    def __init__(self, app, version: APIVersion):
        self.app = app
        self.version = version
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # 检查版本状态
            version_info = api_registry.get_version_info(self.version)
            
            if version_info and version_info.status == APIStatus.DEPRECATED:
                # 构建警告消息
                warning_message = f"API version {self.version.value} is deprecated"
                if version_info.removed_in:
                    warning_message += f" and will be removed in {version_info.removed_in.value}"
                
                # 添加警告头
                def add_warning_header(message):
                    async def app_with_warning(scope, receive, send):
                        async def send_with_warning(message):
                            if message["type"] == "http.response.start":
                                headers = list(message.get("headers", []))
                                headers.append([b"X-API-Warning", warning_message.encode()])
                                message["headers"] = headers
                            await send(message)
                        
                        await self.app(scope, receive, send_with_warning)
                    return app_with_warning
                
                app_with_warning = add_warning_header(warning_message)
                return await app_with_warning(scope, receive, send)
        
        return await self.app(scope, receive, send)


def create_versioned_router(
    version: APIVersion,
    prefix: str = "",
    tags: Optional[List[str]] = None,
    **kwargs
) -> VersionedAPIRouter:
    """创建版本化路由器"""
    return VersionedAPIRouter(
        version=version,
        prefix=prefix,
        tags=tags,
        **kwargs
    )