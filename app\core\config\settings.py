import os
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# 确保在应用根目录加载.env文件
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
load_dotenv(PROJECT_ROOT / ".env")

from app.core.config.app_config import AppConfig
from app.core.config.database_config import DatabaseConfig
from app.core.config.security_config import SecurityConfig
from app.core.config.logging_config import LoggingConfig
from app.core.config.enums import Environment


class Settings(BaseSettings):
    """主配置类"""
    
    model_config = SettingsConfigDict(
        extra='ignore',  # 忽略额外的字段而不是抛出错误
        case_sensitive=True,
        env_file=str(Path(__file__).parent.parent.parent.parent / ".env"),
        env_file_encoding="utf-8",
        env_nested_delimiter="__"
    )
    
    # 子配置
    app: AppConfig = AppConfig()
    database: DatabaseConfig = DatabaseConfig()
    security: SecurityConfig = SecurityConfig()
    logging: LoggingConfig = LoggingConfig()
    
    # 外部服务配置
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # Elasticsearch配置
    ELASTICSEARCH_URL: Optional[str] = None
    ELASTICSEARCH_API_KEY: str = "None"
    ELASTICSEARCH_INDEX_PREFIX: str = "biohub_ai_"
    
    # AI服务配置
    DASHSCOPE_API_KEY: str = ""
    
    # 存储配置
    UPLOAD_DIR: str = "./uploads"
    MAX_UPLOAD_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    @field_validator("REDIS_PORT")
    def validate_redis_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("Redis port must be between 1 and 65535")
        return v
    
    @field_validator("MAX_UPLOAD_SIZE")
    def validate_upload_size(cls, v):
        if v < 1024:  # 1KB minimum
            raise ValueError("Max upload size must be at least 1KB")
        return v
    
    def __init__(self, **data):
        super().__init__(**data)
        self._validate_all_configs()
        self._ensure_directories()
    
    def _validate_all_configs(self):
        """验证所有子配置"""
        self.app.validate_config()
        self.database.validate_config()
        self.security.validate_config()
        self.logging.validate_config()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.UPLOAD_DIR,
            os.path.dirname(self.logging.LOG_FILE),
            os.path.dirname(self.logging.ERROR_LOG_FILE),
            os.path.dirname(self.logging.ACCESS_LOG_FILE),
        ]
        
        for directory in directories:
            if directory:
                os.makedirs(directory, exist_ok=True)
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息（不包含敏感信息）"""
        return {
            "app": self.app.get_config_info(),
            "database": self.database.get_config_info(),
            "security": self.security.get_config_info(),
            "logging": self.logging.get_config_info(),
            "redis": {
                "host": self.REDIS_HOST,
                "port": self.REDIS_PORT,
                "db": self.REDIS_DB,
            },
            "elasticsearch": {
                "url": self.ELASTICSEARCH_URL,
                "index_prefix": self.ELASTICSEARCH_INDEX_PREFIX,
            },
            "storage": {
                "upload_dir": self.UPLOAD_DIR,
                "max_upload_size": self.MAX_UPLOAD_SIZE,
            },
        }
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.app.ENVIRONMENT == Environment.PRODUCTION
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.app.ENVIRONMENT == Environment.DEVELOPMENT
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.app.ENVIRONMENT == Environment.TESTING


# 全局设置实例
settings = Settings()