import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

from agno.document.base import Document as AgnoDocument
from agno.document.chunking.recursive import RecursiveChunking
from agno.embedder import Embedder
from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.infrastructure.external_services.es import DocumentChunkService
from app.core.logging.log_config import get_logger
from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud
from app.infrastructure.database.repositories.crud_knowledge_base import document_chunk as document_chunk_crud
from app.domain.knowledge_base.models.knowledge_base import Document, DocumentChunk
from app.shared.schemas.knowledge_base import DocumentChunkCreate, DocumentCreate
from app.shared.utils.docx_tools import docx_to_html

logger = get_logger("app.services.document")


class KnowledgeBaseDocumentService:
    """文档处理服务类"""

    def __init__(
        self,
        embedding_model: Optional[Embedder] = None,
        max_chunk_size: Optional[int] = 512,
    ):
        """
        初始化文档服务

        参数:
            embedding_model: 用于生成文本嵌入向量的模型实例
            max_chunk_size: 每个文本块的最大令牌数
        """
        self.max_chunk_size = max_chunk_size
        self.embedding_model = embedding_model
        self.es_service = DocumentChunkService()

        # 初始化RecursiveChunking
        self.chunking_strategy = RecursiveChunking(
            chunk_size=max_chunk_size, overlap=50
        )
        logger.info("使用RecursiveChunking分块策略")

    async def upload_and_process_document(
        self, db: Session, knowledge_base_id: int, file: UploadFile, user_id: str
    ) -> Document:
        """
        上传并处理文档，包括分块和存储到ES

        参数:
            db: 数据库会话
            knowledge_base_id: 知识库ID
            file: 上传的文件
            user_id: 用户ID

        返回:
            创建的文档对象
        """
        try:
            logger.info(f"开始处理文档上传: {file.filename}")

            # 1. 验证文件类型
            if not self._is_supported_file_type(file.filename):
                raise ValueError(f"不支持的文件类型: {file.filename}")

            # 2. 读取文件内容
            file_content = await file.read()

            # 3. 提取文本内容
            text_content = await self._extract_text_content(file, file_content)
            logger.debug(f"提取到文本内容: {text_content[0:50]}...")

            # 4. 创建文档记录
            document_create = DocumentCreate(
                title=file.filename,
                description=f"上传的文档: {file.filename}",
                source="upload",
                source_type=self._get_file_type(file.filename),
                uploaded_by=user_id,  # 设置上传用户ID
                metadata={
                    "original_filename": file.filename,
                    "file_size": len(file_content),
                    "upload_user_id": user_id,
                    "upload_time": datetime.utcnow().isoformat(),
                },
            )

            document = await document_crud.create_with_knowledge_base(
                db=db, obj_in=document_create, knowledge_base_id=knowledge_base_id
            )

            logger.info(f"文档记录创建成功，ID: {document.id}")

            # 5. 进行文本分块
            chunks = await self._chunk_text(text_content, document)

            # 6. 保存分块到数据库和ES
            await self._save_chunks_to_db_and_es(
                db=db,
                document_id=document.id,
                knowledge_base_id=knowledge_base_id,
                chunks=chunks,
            )

            logger.info(f"文档处理完成，共生成 {len(chunks)} 个分块")
            return document

        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            raise

    async def _extract_text_content(self, file: UploadFile, file_content: bytes) -> str:
        """
        从文件中提取文本内容

        参数:
            file: 上传的文件对象
            file_content: 文件内容字节

        返回:
            提取的文本内容
        """
        file_type = self._get_file_type(file.filename)

        try:
            if file_type == "txt":
                # 处理纯文本文件
                return file_content.decode("utf-8")

            elif file_type == "docx":
                # 处理Word文档
                # 先保存临时文件
                temp_path = f"/tmp/{file.filename}"
                with open(temp_path, "wb") as temp_file:
                    temp_file.write(file_content)

                try:
                    # 使用docx_tools转换为HTML，然后提取文本
                    html_content = docx_to_html(temp_path)
                    # 简单的HTML标签清理（实际项目中可能需要更复杂的处理）
                    import re

                    text_content = re.sub(r"<[^>]+>", "", html_content)
                    text_content = re.sub(r"\s+", " ", text_content).strip()
                    return text_content
                finally:
                    # 清理临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

            elif file_type == "pdf":
                # PDF处理
                import pymupdf  # PyMuPDF

                try:
                    # 从字节流中打开PDF文档
                    pdf_document = pymupdf.open(stream=file_content, filetype="pdf")
                    text_content = ""
                    for page_num in range(len(pdf_document)):
                        page = pdf_document.load_page(page_num)
                        text_content += page.get_text()
                    return text_content
                except Exception as e:
                    logger.error(f"PDF内容提取失败: {str(e)}")
                    raise ValueError(f"无法从PDF文件中提取文本内容: {str(e)}")

            else:
                raise ValueError(f"不支持的文件类型: {file_type}")

        except Exception as e:
            logger.error(f"文本提取失败: {str(e)}")
            raise ValueError(f"无法从文件中提取文本内容: {str(e)}")

    async def _chunk_text(self, text_content: str, document: Document) -> List[str]:
        """
        对文本进行分块处理

        参数:
            text_content: 要分块的文本内容
            document: 文档对象

        返回:
            分块后的文本列表
        """
        try:
            # 使用RecursiveChunking进行分块
            # 创建Agno文档对象
            agno_doc = AgnoDocument(
                id=str(document.id),
                name=document.title,
                content=text_content,
                meta_data={
                    "document_id": document.id,
                    "knowledge_base_id": document.knowledge_base_id,
                },
            )

            # 执行分块
            chunk_docs = self.chunking_strategy.chunk(agno_doc)

            # 提取分块文本
            chunks = [chunk_doc.content for chunk_doc in chunk_docs]

            logger.info(f"分块完成，共生成 {len(chunks)} 个分块")
            return chunks

        except Exception as e:
            logger.error(f"文本分块失败: {str(e)}")
            # 降级到简单分块
            logger.info("降级使用简单分块策略")
            return self._simple_chunk_text(text_content)

    def _simple_chunk_text(self, text: str) -> List[str]:
        """
        简单的文本分块策略（按字符数分割）

        参数:
            text: 要分块的文本

        返回:
            分块后的文本列表
        """
        chunks = []
        chunk_size = self.max_chunk_size

        # 按段落分割
        paragraphs = text.split("\n\n")
        current_chunk = ""

        for paragraph in paragraphs:
            # 如果当前块加上新段落不超过限制，则添加
            if len(current_chunk) + len(paragraph) <= chunk_size:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk.strip())

                # 如果单个段落超过限制，需要进一步分割
                if len(paragraph) > chunk_size:
                    # 按句子分割
                    sentences = paragraph.split(". ")
                    temp_chunk = ""

                    for sentence in sentences:
                        if len(temp_chunk) + len(sentence) <= chunk_size:
                            if temp_chunk:
                                temp_chunk += ". " + sentence
                            else:
                                temp_chunk = sentence
                        else:
                            if temp_chunk:
                                chunks.append(temp_chunk.strip())
                            temp_chunk = sentence

                    current_chunk = temp_chunk
                else:
                    current_chunk = paragraph

        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk.strip())

        return [chunk for chunk in chunks if chunk.strip()]

    async def _save_chunks_to_db_and_es(
        self, db: Session, document_id: int, knowledge_base_id: int, chunks: List[str]
    ):
        """
        将分块保存到数据库和Elasticsearch

        参数:
            db: 数据库会话
            document_id: 文档ID
            knowledge_base_id: 知识库ID
            chunks: 分块文本列表
        """
        try:
            for i, chunk_content in enumerate(chunks):
                # 1. 保存到数据库
                chunk_create = DocumentChunkCreate(
                    content=chunk_content,
                    metadata={
                        "chunk_index": i,
                        "chunk_size": len(chunk_content),
                        "created_at": datetime.utcnow().isoformat(),
                    },
                )

                db_chunk = DocumentChunk(
                    document_id=document_id,
                    content=chunk_content,
                    chunk_index=i,
                    cmetadata=chunk_create.metadata,
                )

                db.add(db_chunk)
                db.flush()  # 获取ID但不提交

                # 2. 保存到Elasticsearch
                # 使用BailianEmbedding生成文本嵌入向量
                embedding = self.embedding_model.get_embedding(chunk_content)

                es_chunk_data = {
                    "knowledge_base_id": knowledge_base_id,
                    "document_id": document_id,
                    "chunk_id": db_chunk.id,
                    "title": f"Chunk {i + 1}",
                    "content": chunk_content,
                    "metadata": json.dumps(chunk_create.metadata),
                    "created_at": datetime.utcnow().isoformat(),
                    "embedding": embedding,
                }

                await self.es_service.index_document_chunk(es_chunk_data)

                logger.debug(f"分块 {i + 1} 保存成功")

            # 提交数据库事务
            db.commit()
            logger.info("所有分块已保存到数据库和ES")

        except Exception as e:
            db.rollback()
            logger.error(f"保存分块失败: {str(e)}")
            raise

    def _is_supported_file_type(self, filename: str) -> bool:
        """
        检查文件类型是否支持

        参数:
            filename: 文件名

        返回:
            是否支持该文件类型
        """
        supported_types = {"txt", "docx", "pdf", "md"}
        file_type = self._get_file_type(filename)
        return file_type in supported_types

    def _get_file_type(self, filename: str) -> str:
        """
        获取文件类型

        参数:
            filename: 文件名

        返回:
            文件类型（扩展名）
        """
        if not filename or "." not in filename:
            return "unknown"
        return filename.rsplit(".", 1)[1].lower()

    async def delete_document_and_chunks(self, db: Session, document_id: int):
        """
        删除文档及其所有分块

        参数:
            db: 数据库会话
            document_id: 文档ID
        """
        try:
            # 1. 从ES中删除分块
            await self.es_service.delete_document_chunks(document_id)

            # 2. 从数据库中删除分块
            db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document_id
            ).delete()

            # 3. 删除文档
            await document_crud.remove(db, id=document_id)

            db.commit()
            logger.info(f"文档 {document_id} 及其分块已删除")

        except Exception as e:
            db.rollback()
            logger.error(f"删除文档失败: {str(e)}")
            raise

    async def get_document_chunks(
        self, db: Session, document_id: int, skip: int = 0, limit: int = 100
    ) -> List[DocumentChunk]:
        """
        获取文档的分块列表

        参数:
            db: 数据库会话
            document_id: 文档ID
            skip: 跳过的记录数
            limit: 限制返回的记录数

        返回:
            文档分块列表
        """
        return await document_chunk_crud.get_multi_by_document(
            db=db, document_id=document_id, skip=skip, limit=limit
        )

    async def hybrid_search(
        self,
        query: str,
        knowledge_base_id: Optional[int] = None,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        top_k: int = 5,
        min_score: float = 0.5,
    ) -> List[Dict[str, Any]]:
        """
        混合搜索方法，结合向量搜索和关键字搜索

        参数:
            query: 查询文本
            knowledge_base_id: 知识库ID，可选
            vector_weight: 向量搜索权重，默认0.7
            keyword_weight: 关键字搜索权重，默认0.3
            top_k: 返回结果数量，默认5
            min_score: 最小相关性分数，默认0.5

        返回:
            搜索结果列表，按相关性排序
        """
        try:
            # 1. 生成查询向量
            query_vector = self.embedding_model.get_embedding(query)

            # 2. 执行向量搜索
            vector_results = await self.es_service.search_by_vector(
                query_vector=query_vector,
                knowledge_base_id=knowledge_base_id,
                top_k=top_k * 2,  # 获取更多结果用于后续合并
            )

            # 3. 执行关键字搜索
            keyword_results = await self.es_service.search_by_keyword(
                keyword=query,
                knowledge_base_id=knowledge_base_id,
                top_k=top_k * 2,  # 获取更多结果用于后续合并
            )

            # 4. 合并结果并计算混合得分
            merged_results = self._merge_search_results(
                vector_results=vector_results,
                keyword_results=keyword_results,
                vector_weight=vector_weight,
                keyword_weight=keyword_weight,
            )

            # 5. 过滤低分结果并返回前top_k个
            filtered_results = [r for r in merged_results if r["score"] >= min_score]
            sorted_results = sorted(
                filtered_results, key=lambda x: x["score"], reverse=True
            )[:top_k]

            logger.info(f"混合搜索完成，共找到 {len(sorted_results)} 个结果")
            return sorted_results

        except Exception as e:
            logger.error(f"混合搜索失败: {str(e)}")
            raise

    def _merge_search_results(
        self,
        vector_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
    ) -> List[Dict[str, Any]]:
        """
        合并向量搜索和关键字搜索结果

        参数:
            vector_results: 向量搜索结果
            keyword_results: 关键字搜索结果
            vector_weight: 向量搜索权重
            keyword_weight: 关键字搜索权重

        返回:
            合并后的结果列表
        """
        # 创建结果字典，键为chunk_id
        result_dict = {}

        # 处理向量搜索结果
        for i, result in enumerate(vector_results):
            chunk_id = result["chunk_id"]
            # 归一化分数：位置越靠前，分数越高
            normalized_score = 1.0 - (i / len(vector_results)) if vector_results else 0

            result_dict[chunk_id] = {
                **result,
                "vector_score": normalized_score,
                "keyword_score": 0.0,
                "score": normalized_score * vector_weight,
            }

        # 处理关键字搜索结果
        for i, result in enumerate(keyword_results):
            chunk_id = result["chunk_id"]
            # 归一化分数：位置越靠前，分数越高
            normalized_score = (
                1.0 - (i / len(keyword_results)) if keyword_results else 0
            )

            if chunk_id in result_dict:
                # 如果结果已存在，更新关键字分数和总分
                result_dict[chunk_id]["keyword_score"] = normalized_score
                result_dict[chunk_id]["score"] += normalized_score * keyword_weight
            else:
                # 如果结果不存在，添加新结果
                result_dict[chunk_id] = {
                    **result,
                    "vector_score": 0.0,
                    "keyword_score": normalized_score,
                    "score": normalized_score * keyword_weight,
                }

        # 转换为列表并返回
        return list(result_dict.values())
