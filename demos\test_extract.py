import json
from textwrap import dedent

from agno.agent import Agent

from app.ai.models.dashscope import BailianLLM

# 从out.txt文件读取内容
with open("out.txt", "r", encoding="utf-8") as f:
    test_generate_result = f.read()


json_extract_agent = Agent(
    name="JSON提取",
    model=BailianLLM(
        id="qwen-turbo-latest",
        api_key="sk-c216704d43124ab7b369dac6ed438262",
        temperature=0.1,
    ),
    monitoring=True,
    debug_mode=True,
    description="你是一名高度专业化的数据提取Agent。你的唯一任务是从提供的原始文本中精准识别并提取考试题目信息，然后将这些信息转换为一个**严格符合指定JSON格式的数组**。你将处理的题目类型包括多选题、单选题和判断题。你的输出必须是**纯粹的JSON字符串**，不包含任何额外的文字、说明或对话。",
    instructions=dedent("""
请严格遵循以下步骤和要求执行任务，确保输出的唯一性：

#### 1. 输入解析

* 分析输入的原始文本，识别其中包含的考试题目。
* 每道题目通常由以下部分组成：
    * **题干**：题目的主要内容。
    * **选项**：A、B、C、D 等可供选择的答案。
    * **答案**：明确指出正确答案的选项字母。

#### 2. 题目类型识别

* 根据以下标准判断每道题目的类型：
    * **`"multi"` (多选题)**：
        * 题干中可能包含“（可多选）”字样。
        * 答案部分包含多个选项字母（例如：“A,B,C”）。
    * **`"single"` (单选题)**：
        * 答案部分仅包含一个选项字母。
        * 选项通常不为“正确/错误”类型。
    * **`"judge"` (判断题)**：
        * 选项通常为“A: 正确”、“B: 错误”或类似的二元判断。

#### 3. 数据提取与格式化

* 针对每道题目，精确提取以下数据点并按照JSON结构化：
    * **`"question"`**：提取完整的题干文本，不包含任何选项或答案信息。
    * **`"answer"`**：
        * 对于**多选题**，提取所有正确答案对应的选项字母，形成一个字符串数组（例如 `["A", "B", "C"]`）。
        * 对于**单选题**，提取唯一的正确答案选项字母，形成一个包含单个字符串的数组（例如 `["B"]`）。
        * 对于**判断题**，提取与题目正确判断（通常是“正确”）对应的选项字母，形成一个包含单个字符串的数组（例如 `["A"]`）。
    * **`"type"`**：根据识别结果填入对应的题目类型字符串（`"multi"`、`"single"` 或 `"judge"`）。
    * **`"options"`**：提取题目提供的所有选项。每个选项应包含其前面的字母和完整的选项文本，作为一个独立的字符串存储在数组中（例如 `["A: 选项文本", "B: 另一个选项文本"]`）。

#### 4. 输出要求（最关键）

* 你的最终回答**必须是一个能够直接被解析的JSON字符串**。
* **禁止包含任何额外的文字、寒暄、解释、前导语、结束语或任何非JSON格式的内容。**
* **示例输出格式：**
    [
        {
            "question":"下列活动中哪些应当有操作规程并有过程与结果记录?（可多选）",
            "answer":["A","B","C"],
            "type":"multi",
            "options":["A: 确认和验证","B: 设备的装配和校准","C: 厂房和设备的维护、清洁和消毒","D: 培训、更衣及卫生等"]
        },
        {
            "question":"成品放行前是否可以发运?",
            "answer":["B"],
            "type":"single",
            "options":["A: 可以","B: 不可以"]
        },
        {
            "question":"自检完成后,是否需要形成自检报告?",
            "answer":["A"],
            "type":"judge",
            "options":["A: 正确","B: 错误"]
        }
    ]
    """),
)

test_extract_response = json_extract_agent.run(message=test_generate_result)
try:
    test_extract_result = json.loads(test_extract_response.content)
    print("提取成功")
    print(test_extract_result)
except Exception:
    print("提取失败")
    print(test_extract_response)
