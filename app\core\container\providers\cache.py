import redis
from typing import Optional

from app.core.container.container import Container, ServiceProvider
from app.core.config.settings import settings


class CacheServiceProvider(ServiceProvider):
    """缓存服务提供者"""
    
    def register(self, container: Container) -> None:
        """注册缓存服务"""
        
        def create_redis_client():
            """创建Redis客户端"""
            if settings.REDIS_URL:
                return redis.from_url(settings.REDIS_URL)
            else:
                return redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    password=settings.REDIS_PASSWORD,
                    decode_responses=True
                )
        
        def create_cache_service():
            """创建缓存服务"""
            redis_client = container.get("redis_client")
            return CacheService(redis_client)
        
        # 注册服务
        container.singleton("redis_client", create_redis_client)
        container.singleton("cache_service", create_cache_service)


class CacheService:
    """缓存服务"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    def get(self, key: str) -> Optional[str]:
        """获取缓存"""
        try:
            return self.redis.get(key)
        except redis.RedisError:
            return None
    
    def set(self, key: str, value: str, expire: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            return self.redis.set(key, value, ex=expire)
        except redis.RedisError:
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return bool(self.redis.delete(key))
        except redis.RedisError:
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return bool(self.redis.exists(key))
        except redis.RedisError:
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        try:
            return self.redis.flushdb()
        except redis.RedisError:
            return False