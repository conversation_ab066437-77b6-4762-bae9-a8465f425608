import logging
import logging.config
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

from app.core.config import settings


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""

    # 颜色代码
    COLORS = {
        "DEBUG": "\033[36m",  # 青色
        "INFO": "\033[32m",  # 绿色
        "WARNING": "\033[33m",  # 黄色
        "ERROR": "\033[31m",  # 红色
        "CRITICAL": "\033[35m",  # 紫色
        "RESET": "\033[0m",  # 重置
    }

    def format(self, record):
        # 添加颜色
        log_color = self.COLORS.get(record.levelname, self.COLORS["RESET"])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)


class RequestIdFilter(logging.Filter):
    """请求ID过滤器，用于追踪请求"""

    def filter(self, record):
        # 尝试从上下文中获取请求ID
        request_id = getattr(record, "request_id", None)
        if not request_id:
            # 如果没有请求ID，生成一个默认值
            record.request_id = "system"
        return True


def setup_logging():
    """设置日志配置"""

    # 确保日志目录存在
    try:
        log_file = settings.logging.LOG_FILE
    except AttributeError:
        # 如果logging配置不存在，使用默认路径
        log_file = "logs/app.log"
    
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # 日志配置字典
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "detailed": {
                "format": "%(asctime)s | %(name)s | %(levelname)s | %(request_id)s | %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "simple": {"format": "%(levelname)s | %(message)s"},
            "colored": {
                "()": ColoredFormatter,
                "format": "%(asctime)s | %(name)s | %(levelname)s | %(request_id)s | %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "format": "%(asctime)s %(name)s %(levelname)s %(request_id)s %(message)s",
            },
        },
        "filters": {"request_id": {"()": RequestIdFilter}},
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": getattr(settings, 'logging', None) and getattr(settings.logging, 'LOG_LEVEL', None) or "INFO",
                "formatter": "colored" if sys.stdout.isatty() else "detailed",
                "stream": sys.stdout,
                "filters": ["request_id"],
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": getattr(settings, 'logging', None) and getattr(settings.logging, 'LOG_LEVEL', None) or "INFO",
                "formatter": "detailed",
                "filename": log_file,
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
                "filters": ["request_id"],
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": log_file.replace(".log", "_error.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
                "filters": ["request_id"],
            },
        },
        "loggers": {
            # 应用日志
            "app": {
                "level": getattr(settings, 'logging', None) and getattr(settings.logging, 'LOG_LEVEL', None) or "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            # FastAPI日志
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["file"],
                "propagate": False,
            },
            # SQLAlchemy日志
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            # Elasticsearch日志
            "elasticsearch": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
        },
        "root": {"level": getattr(settings, 'logging', None) and getattr(settings.logging, 'LOG_LEVEL', None) or "INFO", "handlers": ["console", "file"]},
    }

    # 应用配置
    logging.config.dictConfig(logging_config)

    # 获取应用日志器
    logger = logging.getLogger("app")
    log_level = getattr(settings, 'logging', None) and getattr(settings.logging, 'LOG_LEVEL', None) or "INFO"
    logger.info(f"日志系统初始化完成，日志级别: {log_level}")
    logger.info(f"日志文件路径: {log_file}")

    return logger


def get_logger(name: str = "app") -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


def log_function_call(
    func_name: str, args: Dict[str, Any] = None, kwargs: Dict[str, Any] = None
):
    """记录函数调用日志的装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger()
            start_time = datetime.now()

            # 记录函数开始执行
            logger.debug(
                f"开始执行函数: {func_name}, 参数: args={args}, kwargs={kwargs}"
            )

            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                # 记录函数执行成功
                logger.debug(f"函数 {func_name} 执行成功，耗时: {duration:.3f}秒")
                return result

            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                # 记录函数执行失败
                logger.error(
                    f"函数 {func_name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}"
                )
                raise

        return wrapper

    return decorator


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""

    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return get_logger(f"app.{self.__class__.__module__}.{self.__class__.__name__}")