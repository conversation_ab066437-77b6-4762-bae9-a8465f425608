# 生产环境配置示例文件
# 复制此文件为 .env 并填入实际的生产环境配置

# 应用配置
APP__ENVIRONMENT=production
APP__DEBUG=false
APP__HOST=0.0.0.0
APP__PORT=8000
APP__WORKERS=4

# 数据库配置
DATABASE__POSTGRES_SERVER=postgres
DATABASE__POSTGRES_USER=postgres
DATABASE__POSTGRES_PASSWORD=your_strong_postgres_password_here
DATABASE__POSTGRES_DB=biohub_prod
DATABASE__POSTGRES_PORT=5432

DATABASE__MYSQL_SERVER=mysql
DATABASE__MYSQL_USER=root
DATABASE__MYSQL_PASSWORD=your_strong_mysql_password_here
DATABASE__MYSQL_DB=biohub_prod
DATABASE__MYSQL_PORT=3306

# 缓存配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_strong_redis_password_here

# 搜索引擎配置
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_API_KEY=your_elasticsearch_api_key_here
ELASTICSEARCH_PASSWORD=your_strong_elasticsearch_password_here
ELASTICSEARCH_INDEX_PREFIX=biohub_prod_

# AI服务配置
DASHSCOPE_API_KEY=your_production_dashscope_api_key_here

# 安全配置
SECURITY__JWT_SECRET_KEY=your_jwt_secret_key_at_least_32_characters_long_here
SECURITY__SECRET_KEY=your_app_secret_key_here
SECURITY__SSL_ENABLED=true
SECURITY__RATE_LIMIT_ENABLED=true

# 日志配置
LOGGING__LOG_LEVEL=INFO
LOGGING__STRUCTURED_LOGGING=true
LOGGING__JSON_LOGS=true
LOGGING__CONSOLE_LOG_ENABLED=false

# 存储配置
UPLOAD_DIR=/app/uploads
MAX_UPLOAD_SIZE=104857600

# 监控配置
GRAFANA_PASSWORD=your_grafana_admin_password_here

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_TLS=true

# 外部服务配置
AUTH_SERVICE_URL=https://your-auth-service.com/api

# CORS配置
CORS_ORIGINS=["https://your-frontend-domain.com"]