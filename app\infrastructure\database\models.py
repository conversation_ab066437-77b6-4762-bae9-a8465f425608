from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, Integer, DateTime, String, Boolean, Text, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.infrastructure.database.session import Base


class TimestampMixin:
    """时间戳混合类"""
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    @hybrid_property
    def created_at_timestamp(self):
        """创建时间戳"""
        return int(self.created_at.timestamp()) if self.created_at else None
    
    @hybrid_property
    def updated_at_timestamp(self):
        """更新时间戳"""
        return int(self.updated_at.timestamp()) if self.updated_at else None


class SoftDeleteMixin:
    """软删除混合类"""
    
    deleted_at = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """恢复"""
        self.is_deleted = False
        self.deleted_at = None
    
    @hybrid_property
    def is_active(self):
        """是否活跃"""
        return not self.is_deleted


class UserTrackingMixin:
    """用户跟踪混合类"""
    
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    
    def set_created_by(self, user_id: str):
        """设置创建者"""
        self.created_by = user_id
    
    def set_updated_by(self, user_id: str):
        """设置更新者"""
        self.updated_by = user_id


class BaseModel(Base, TimestampMixin, SoftDeleteMixin, UserTrackingMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    
    @declared_attr
    def __tablename__(cls):
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: Optional[list] = None):
        """从字典更新"""
        exclude_fields = exclude_fields or ['id', 'created_at', 'updated_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    @classmethod
    def get_column_names(cls) -> list:
        """获取所有列名"""
        return [column.name for column in cls.__table__.columns]
    
    @classmethod
    def get_required_fields(cls) -> list:
        """获取必填字段"""
        return [
            column.name for column in cls.__table__.columns
            if not column.nullable and column.default is None and column.name != 'id'
        ]
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"


class UUIDMixin:
    """UUID混合类"""
    
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    
    @hybrid_property
    def uuid_str(self):
        """UUID字符串"""
        return str(self.uuid) if self.uuid else None


class VersionMixin:
    """版本控制混合类"""
    
    version = Column(Integer, default=1, nullable=False)
    
    def increment_version(self):
        """增加版本号"""
        self.version += 1


class MetadataMixin:
    """元数据混合类"""
    
    metadata_json = Column(Text, nullable=True)
    
    @hybrid_property
    def metadata_dict(self) -> Dict[str, Any]:
        """元数据字典"""
        if self.metadata_json:
            import json
            try:
                return json.loads(self.metadata_json)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    @metadata_dict.setter
    def metadata_dict(self, value: Dict[str, Any]):
        """设置元数据字典"""
        import json
        self.metadata_json = json.dumps(value) if value else None
    
    def set_metadata(self, key: str, value: Any):
        """设置元数据"""
        metadata = self.metadata_dict
        metadata[key] = value
        self.metadata_dict = metadata
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self.metadata_dict.get(key, default)


class SlugMixin:
    """Slug混合类"""
    
    slug = Column(String(255), unique=True, nullable=True, index=True)
    
    def generate_slug(self, source_field: str = 'name'):
        """生成slug"""
        import re
        
        source_value = getattr(self, source_field, '')
        if not source_value:
            return
        
        # 转换为小写并替换空格和特殊字符
        slug = re.sub(r'[^\w\s-]', '', source_value.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        slug = slug.strip('-')
        
        # 确保唯一性
        base_slug = slug
        counter = 1
        while self.__class__.query.filter_by(slug=slug).first():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        self.slug = slug


class StatusMixin:
    """状态混合类"""
    
    status = Column(String(50), default='active', nullable=False)
    
    def set_status(self, status: str):
        """设置状态"""
        self.status = status
    
    def is_status(self, status: str) -> bool:
        """检查状态"""
        return self.status == status
    
    def activate(self):
        """激活"""
        self.status = 'active'
    
    def deactivate(self):
        """停用"""
        self.status = 'inactive'


class OrderMixin:
    """排序混合类"""
    
    sort_order = Column(Integer, default=0, nullable=False)
    
    def set_order(self, order: int):
        """设置排序"""
        self.sort_order = order
    
    @classmethod
    def get_next_order(cls, session) -> int:
        """获取下一个排序值"""
        max_order = session.query(func.max(cls.sort_order)).scalar()
        return (max_order or 0) + 1


class FullTextSearchMixin:
    """全文搜索混合类"""
    
    search_vector = Column(Text, nullable=True)
    
    def update_search_vector(self, *fields):
        """更新搜索向量"""
        content = []
        for field in fields:
            value = getattr(self, field, '')
            if value:
                content.append(str(value))
        
        self.search_vector = ' '.join(content)
    
    @classmethod
    def search(cls, session, query: str, *fields):
        """搜索"""
        if not fields:
            fields = ['search_vector']
        
        conditions = []
        for field in fields:
            if hasattr(cls, field):
                conditions.append(getattr(cls, field).contains(query))
        
        if conditions:
            return session.query(cls).filter(or_(*conditions))
        return session.query(cls).filter(False)


# 组合模型类
class EnhancedBaseModel(BaseModel, UUIDMixin, VersionMixin, MetadataMixin):
    """增强基础模型类"""
    __abstract__ = True