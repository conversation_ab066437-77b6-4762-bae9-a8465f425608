from typing import Any

from fastapi import (
    APIRouter,
    Depends,
    File,
    Query,
    Request,
    UploadFile,
    status,
)
from sqlalchemy.orm import Session

from app.shared import schemas
from app.domain.ai.services.embeddings.dashscope import BailianEmbeddings
from app.api.v1.dependencies import deps
from app.core.config import settings
from app.core.exceptions.exceptions import (
    AuthorizationException,
    BusinessException,
    ResourceNotFoundException,
)
from app.core.logging.log_config import get_logger
from app.core.logging.log_utils import log_api_call
from app.shared.utils.response_utils import (
    created_response,
    success_response,
)
from app.infrastructure.database.repositories.crud_knowledge_base import knowledge_base as knowledge_base_crud
from app.infrastructure.database.repositories.crud_user import user as crud_user
from app.shared.schemas.user import UserBase
from app.domain.knowledge_base.services.document_service import KnowledgeBaseDocumentService

router = APIRouter()
logger = get_logger("app.api.knowledge_base")


@log_api_call
@router.get(
    "/list",
    summary="获取知识库列表",
    description="获取当前用户可访问的所有知识库列表，包括用户创建的、作为成员的以及公开的知识库。",
)
async def read_knowledge_bases(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取知识库列表

    获取当前用户可访问的所有知识库列表，包括：
    1. 用户创建的知识库（作为所有者）
    2. 用户作为成员的知识库
    3. 公开的知识库

    Args:
        request: FastAPI请求对象，用于获取请求ID
        skip: 跳过的记录数，用于分页，默认为0
        limit: 返回的记录数，用于分页，范围1-1000，默认为100
        db: 数据库会话对象
        current_user_id: 当前用户ID，从认证中间件获取

    Returns:
        包含知识库列表的成功响应，数据结构为知识库对象列表

    Note:
        - 会自动去重，避免同一知识库在多个类别中重复出现
        - 返回的知识库按照获取顺序排列
    """
    request_id = getattr(request.state, "request_id", "unknown")
    logger.info(
        f"用户 {current_user_id} 请求知识库列表", extra={"request_id": request_id}
    )

    # 获取用户创建的知识库
    user_knowledge_bases = await knowledge_base_crud.get_multi_by_owner(
        db, owner_id=current_user_id, skip=skip, limit=limit
    )

    # 获取用户作为成员的知识库
    member_knowledge_bases = await knowledge_base_crud.get_multi_by_member(
        db, user_id=current_user_id, skip=skip, limit=limit
    )

    # 获取公开的知识库
    public_knowledge_bases = await knowledge_base_crud.get_public(
        db, skip=skip, limit=limit
    )

    # 合并并去重
    all_knowledge_bases = {}
    for kb in user_knowledge_bases + member_knowledge_bases + public_knowledge_bases:
        if kb.id not in all_knowledge_bases:
            all_knowledge_bases[kb.id] = kb

    knowledge_bases_list = list(all_knowledge_bases.values())

    return success_response(data=knowledge_bases_list, msg="获取知识库列表成功")


@log_api_call
@router.get(
    "/options",
    summary="获取知识库选项列表",
    description="获取当前用户可访问的所有知识库的简化列表（ID和名称），用于下拉选项等场景。",
)
async def read_knowledge_base_options(
    request: Request,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取知识库选项列表

    获取当前用户可访问的所有知识库的简化列表，仅包含ID和名称，
    主要用于下拉选项、选择器等UI组件场景。

    Args:
        request: FastAPI请求对象，用于获取请求ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，从认证中间件获取

    Returns:
        包含知识库选项列表的成功响应，每个选项包含id和name字段

    Note:
        - 固定获取最多1000个知识库，假设用户知识库数量不会太多
        - 会自动去重，避免同一知识库重复出现
        - 返回格式为 [{"id": kb_id, "name": kb_name}, ...]
    """
    request_id = getattr(request.state, "request_id", "unknown")
    logger.info(
        f"用户 {current_user_id} 请求知识库选项列表", extra={"request_id": request_id}
    )

    # 获取用户创建的知识库
    user_knowledge_bases = await knowledge_base_crud.get_multi_by_owner(
        db,
        owner_id=current_user_id,
        skip=0,
        limit=1000,  # 获取所有，假设用户知识库不会太多
    )

    # 获取用户作为成员的知识库
    member_knowledge_bases = await knowledge_base_crud.get_multi_by_member(
        db, user_id=current_user_id, skip=0, limit=1000
    )

    # 获取公开的知识库
    public_knowledge_bases = await knowledge_base_crud.get_public(
        db, skip=0, limit=1000
    )

    # 合并并去重，并转换为选项格式
    all_knowledge_bases_options = {}
    for kb in user_knowledge_bases + member_knowledge_bases + public_knowledge_bases:
        if kb.id not in all_knowledge_bases_options:
            all_knowledge_bases_options[kb.id] = {"id": kb.id, "name": kb.name}

    knowledge_base_options_list = list(all_knowledge_bases_options.values())

    return success_response(
        data=knowledge_base_options_list, msg="获取知识库选项列表成功"
    )


@log_api_call
@router.post(
    "/create",
    summary="创建新的知识库",
    description="创建一个新的知识库，并指定其名称、描述和可见性等属性。",
)
async def create_knowledge_base(
    *,
    request: Request,
    db: Session = Depends(deps.get_session),
    knowledge_base_in: schemas.KnowledgeBaseCreate,
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    创建新的知识库

    创建一个新的知识库，当前用户将自动成为该知识库的所有者。

    Args:
        request: FastAPI请求对象，用于获取请求ID
        db: 数据库会话对象
        knowledge_base_in: 知识库创建数据，包含名称、描述、可见性等信息
        current_user_id: 当前用户ID，将作为知识库所有者

    Returns:
        包含新创建知识库信息的创建成功响应（HTTP 201）

    Note:
        - 创建者自动成为知识库所有者
        - 可见性可设置为public（公开）或private（私有）
        - 创建成功后可以立即上传文档和管理成员
    """
    request_id = getattr(request.state, "request_id", "unknown")
    logger.info(
        f"用户 {current_user_id} 创建知识库: {knowledge_base_in.name}",
        extra={"request_id": request_id},
    )

    knowledge_base = await knowledge_base_crud.create_with_owner(
        db, obj_in=knowledge_base_in, owner_id=current_user_id
    )

    return created_response(data=knowledge_base, msg="知识库创建成功")


@router.get(
    "/detail/{knowledge_base_id}",
    summary="获取指定知识库详情",
    description="根据知识库ID获取其详细信息，包括名称、描述、所有者、成员和文档等。",
)
async def read_knowledge_base(
    knowledge_base_id: int,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取指定知识库详情

    根据知识库ID获取其详细信息，包括名称、描述、所有者、可见性等。
    需要相应的访问权限。

    Args:
        knowledge_base_id: 知识库ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含知识库详细信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有访问权限

    Note:
        - 公开知识库所有人都可以访问
        - 私有知识库只有所有者和成员可以访问
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查访问权限
    if knowledge_base.visibility == "public":
        # 公开知识库，所有人都可以访问
        pass
    elif knowledge_base.owner_id != current_user_id:
        # 非公开知识库，检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not is_member:
            raise AuthorizationException("没有访问权限")

    return success_response(data=knowledge_base, msg="获取知识库详情成功")


@log_api_call
@router.put(
    "/update/{knowledge_base_id}",
    summary="更新知识库信息",
    description="更新指定知识库的名称、描述或可见性等信息。需要所有者或管理员权限。",
)
async def update_knowledge_base(
    knowledge_base_id: int,
    knowledge_base_in: schemas.KnowledgeBaseUpdate,
    db: Session = Depends(deps.get_session),
    current_user: UserBase = Depends(deps.get_current_user),
) -> Any:
    """
    更新知识库信息

    更新指定知识库的名称、描述、可见性等信息。
    需要所有者或管理员权限。

    Args:
        knowledge_base_id: 知识库ID
        knowledge_base_in: 知识库更新数据
        db: 数据库会话对象
        current_user: 当前用户信息，用于权限验证

    Returns:
        包含更新后知识库信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有更新权限（非所有者且非管理员）

    Note:
        - 只有知识库所有者和管理员可以更新知识库信息
        - 可更新的字段包括名称、描述、可见性等
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查权限：所有者或管理员
    if knowledge_base.owner_id != current_user.id:
        member_role = await knowledge_base_crud.get_member_role(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user.id
        )
        if member_role not in [schemas.MemberRole.ADMIN]:
            raise AuthorizationException("没有更新知识库的权限")

    knowledge_base = await knowledge_base_crud.update(
        db, db_obj=knowledge_base, obj_in=knowledge_base_in
    )

    return success_response(data=knowledge_base, msg="知识库更新成功")


@log_api_call
@router.delete(
    "/delete/{knowledge_base_id}",
    summary="删除知识库",
    description="删除指定的知识库。只有知识库所有者才能执行此操作。",
)
async def delete_knowledge_base(
    knowledge_base_id: int,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    删除知识库

    删除指定的知识库及其所有相关数据（文档、分块、成员关系等）。
    只有知识库所有者才能执行此操作。

    Args:
        knowledge_base_id: 知识库ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含已删除知识库信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有删除权限（非所有者）

    Warning:
        - 此操作不可逆，会删除知识库及其所有相关数据
        - 包括所有文档、文档分块、成员关系等
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查是否为所有者
    if knowledge_base.owner_id != current_user_id:
        raise AuthorizationException("只有知识库所有者可以删除知识库")

    deleted_knowledge_base = await knowledge_base_crud.remove(db, id=knowledge_base_id)

    return success_response(data=deleted_knowledge_base, msg="知识库删除成功")


@log_api_call
@router.get(
    "/{knowledge_base_id}/members",
    summary="获取知识库成员列表",
    description="获取指定知识库的成员列表及其角色。需要相应的访问权限。",
)
async def read_knowledge_base_members(
    knowledge_base_id: int,
    db: Session = Depends(deps.get_session),
    current_user: UserBase = Depends(deps.get_current_user),
) -> Any:
    """
    获取知识库成员列表

    获取指定知识库的成员列表及其角色信息。
    需要相应的访问权限（所有者或成员）。

    Args:
        knowledge_base_id: 知识库ID
        db: 数据库会话对象
        current_user: 当前用户信息，用于权限验证

    Returns:
        包含成员列表的成功响应，每个成员包含用户信息和角色

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有访问权限（非所有者且非成员）

    Note:
        - 返回的成员列表包含用户ID、姓名、角色等信息
        - 角色包括：owner（所有者）、admin（管理员）、editor（编辑者）、viewer（查看者）
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在",
        )

    # 检查访问权限
    if knowledge_base.owner_id != current_user.id:
        # 检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user["id"]
        )
        if not is_member:
            raise AuthorizationException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有访问权限",
            )

    members = await knowledge_base_crud.get_members(
        db, knowledge_base_id=knowledge_base_id
    )
    return success_response(data=members, msg="获取知识库成员成功")


@log_api_call
@router.post(
    "/{knowledge_base_id}/members",
    summary="添加知识库成员",
    description="向指定的知识库添加新成员，并指定其角色。需要所有者或管理员权限。管理员不能添加管理员角色。",
)
async def add_knowledge_base_member(
    knowledge_base_id: int,
    member_in: schemas.KnowledgeBaseMemberCreate,
    db: Session = Depends(deps.get_session),
    current_user: UserBase = Depends(deps.get_current_user),
) -> Any:
    """
    添加知识库成员

    向指定的知识库添加新成员，并指定其角色。
    需要所有者或管理员权限，管理员不能添加管理员角色。

    Args:
        knowledge_base_id: 知识库ID
        member_in: 成员创建数据，包含用户ID和角色
        db: 数据库会话对象
        current_user: 当前用户信息，用于权限验证

    Returns:
        包含新添加成员信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有添加成员权限或权限不足
        BusinessException: 用户已是成员或添加失败

    Note:
        - 所有者可以添加任何角色的成员
        - 管理员只能添加编辑者和查看者，不能添加其他管理员
        - 不能重复添加已存在的成员
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在",
        )

    # 检查权限：所有者或管理员
    current_user_role = None
    if knowledge_base.owner_id == current_user.id:
        current_user_role = schemas.MemberRole.OWNER
    else:
        current_user_role = await knowledge_base_crud.get_member_role(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user.id
        )

    if current_user_role not in [schemas.MemberRole.OWNER, schemas.MemberRole.ADMIN]:
        raise AuthorizationException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有添加成员的权限",
        )

    # 管理员不能添加管理员角色
    if (
        current_user_role == schemas.MemberRole.ADMIN
        and member_in.role == schemas.MemberRole.ADMIN
    ):
        raise AuthorizationException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="管理员不能添加其他管理员",
        )

    # 检查成员是否已存在
    existing_member = await knowledge_base_crud.is_member(
        db, knowledge_base_id=knowledge_base_id, user_id=member_in.user_id
    )
    if existing_member:
        raise BusinessException(f"用户 {member_in.user_id} 已经是该知识库的成员")

    # 添加成员
    member = await knowledge_base_crud.add_member(
        db,
        knowledge_base_id=knowledge_base_id,
        obj_in=member_in,
    )
    if not member:
        raise BusinessException("添加成员失败")

    return success_response(data=member, msg="添加知识库成员成功")


@log_api_call
@router.delete(
    "/{knowledge_base_id}/members/{user_id}",
    summary="删除知识库成员",
    description="从指定的知识库删除成员。需要所有者或管理员权限。管理员不能删除管理员角色。",
)
async def delete_knowledge_base_member(
    knowledge_base_id: int,
    user_id: str,
    db: Session = Depends(deps.get_session),
    current_user: UserBase = Depends(deps.get_current_user),
) -> Any:
    """
    删除知识库成员

    从指定的知识库删除成员。需要所有者或管理员权限，
    管理员不能删除管理员角色，所有者不能被删除。

    Args:
        knowledge_base_id: 知识库ID
        user_id: 要删除的用户ID
        db: 数据库会话对象
        current_user: 当前用户信息，用于权限验证

    Returns:
        包含已删除成员信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库不存在或用户不是成员
        AuthorizationException: 没有删除成员权限或权限不足
        BusinessException: 尝试删除所有者或删除失败

    Note:
        - 所有者可以删除任何成员（除了自己）
        - 管理员只能删除编辑者和查看者，不能删除其他管理员
        - 知识库所有者不能被删除
    """
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在",
        )

    # 检查权限：所有者或管理员
    current_user_role = None
    if knowledge_base.owner_id == current_user.id:
        current_user_role = schemas.MemberRole.OWNER
    else:
        current_user_role = await knowledge_base_crud.get_member_role(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user.id
        )

    if current_user_role not in [schemas.MemberRole.OWNER, schemas.MemberRole.ADMIN]:
        raise AuthorizationException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有删除成员的权限",
        )

    # 获取待删除成员的角色
    member_to_delete_role = await knowledge_base_crud.get_member_role(
        db, knowledge_base_id=knowledge_base_id, user_id=user_id
    )
    if not member_to_delete_role:
        raise ResourceNotFoundException(f"用户 {user_id} 不是该知识库的成员")

    # 管理员不能删除管理员角色
    if (
        current_user_role == schemas.MemberRole.ADMIN
        and member_to_delete_role == schemas.MemberRole.ADMIN
    ):
        raise AuthorizationException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="管理员不能删除其他管理员",
        )

    # 所有者不能被删除
    if knowledge_base.owner_id == user_id:
        raise BusinessException("知识库所有者不能被删除")

    # 删除成员
    deleted_member = await knowledge_base_crud.remove_member(
        db, knowledge_base_id=knowledge_base_id, user_id=user_id
    )
    if not deleted_member:
        raise BusinessException("删除成员失败")

    return success_response(data=deleted_member, msg="删除知识库成员成功")


@log_api_call()
@router.post(
    "/{knowledge_base_id}/upload_document",
    summary="上传文档到知识库",
    description="上传一个文档文件到指定的知识库。文件将被处理、分块并索引以便后续检索。需要相应的编辑权限。",
)
async def upload_knowledge_base_document(
    knowledge_base_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    上传文档到知识库

    上传一个文档文件到指定的知识库。文件将被处理、分块并索引以便后续检索。
    需要相应的编辑权限（所有者或编辑者）。

    Args:
        knowledge_base_id: 知识库ID
        file: 上传的文件对象
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证和记录上传者

    Returns:
        包含新上传文档信息的创建成功响应（HTTP 201）

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有上传文档权限
        BusinessException: 文件处理失败或其他业务异常

    Note:
        - 支持多种文档格式（PDF、Word、TXT等）
        - 文档会自动分块并生成向量索引
        - 分块大小默认为512字符
        - 上传者信息会被记录在文档中
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查编辑权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为编辑者成员
        member_role = await knowledge_base_crud.get_member_role(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not member_role or member_role not in ["owner", "editor"]:
            raise AuthorizationException("没有上传文档的权限")

    try:
        # 创建文档服务
        document_service = KnowledgeBaseDocumentService(
            embedding_model=BailianEmbeddings(api_key=settings.DASHSCOPE_API_KEY),
            max_chunk_size=512,
        )

        # 上传并处理文档
        document = await document_service.upload_and_process_document(
            db=db,
            knowledge_base_id=knowledge_base_id,
            file=file,
            user_id=current_user_id,
        )

        return created_response(
            data=document, msg="文档上传成功，已生成分块并索引到搜索引擎"
        )

    except ValueError as e:
        raise BusinessException(str(e))
    except Exception as e:
        logger.error(f"文档上传处理失败: {str(e)}")
        raise BusinessException(f"文档处理失败: {str(e)}")


@router.get(
    "/{knowledge_base_id}/documents",
    summary="获取知识库文档列表",
    description="获取指定知识库中的文档列表。需要相应的访问权限。",
)
async def list_knowledge_base_documents(
    knowledge_base_id: int,
    skip: int = 0,
    limit: int = 100,
    pg_db: Session = Depends(deps.get_session),
    mysql_db: Session = Depends(deps.get_mysql_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取知识库文档列表

    获取指定知识库中的文档列表，包含文档基本信息和上传者姓名。
    需要相应的访问权限。

    Args:
        knowledge_base_id: 知识库ID
        skip: 跳过的记录数，用于分页，默认为0
        limit: 返回的记录数，用于分页，默认为100
        pg_db: PostgreSQL数据库会话对象（存储文档信息）
        mysql_db: MySQL数据库会话对象（存储用户信息）
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含文档列表的成功响应，每个文档包含基本信息和上传者姓名

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有访问权限

    Note:
        - 返回的文档信息包含上传者ID和姓名
        - 如果上传者信息不存在，则只显示ID
        - 支持分页查询
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(pg_db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查访问权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            pg_db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not is_member:
            raise AuthorizationException("没有访问权限")

    # 获取文档列表
    from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud

    documents = await document_crud.get_multi_by_knowledge_base(
        pg_db, knowledge_base_id=knowledge_base_id, skip=skip, limit=limit
    )

    response_documents = []
    # 获取用户信息并装填响应结果
    for document in documents:
        response_document = {}
        if document.uploaded_by:
            response_document["uploaded_by"] = document.uploaded_by
            # 从数据库获取用户信息
            user = await crud_user.get_user_by_id(
                mysql_db, user_id=document.uploaded_by
            )
            if user:
                response_document["uploaded_by_name"] = user.name

        response_document["id"] = document.id
        response_document["knowledge_base_id"] = document.knowledge_base_id
        response_document["title"] = document.title
        response_document["description"] = document.description
        response_document["source"] = document.source
        response_document["source_type"] = document.source_type
        response_document["file_path"] = document.file_path
        response_document["content_type"] = document.content_type
        response_document["created_at"] = document.created_at
        response_document["updated_at"] = document.updated_at
        response_document["cmetadata"] = document.cmetadata
        response_documents.append(response_document)

    return success_response(data=response_documents, msg="获取文档列表成功")


@router.get(
    "/{knowledge_base_id}/documents/{document_id}",
    summary="获取知识库文档详情",
    description="获取指定知识库中特定文档的详细信息。需要相应的访问权限。",
)
async def get_knowledge_base_document(
    knowledge_base_id: int,
    document_id: int,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取知识库文档详情

    获取指定知识库中特定文档的详细信息，包括文档内容、元数据等。
    需要相应的访问权限。

    Args:
        knowledge_base_id: 知识库ID
        document_id: 文档ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含文档详细信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库或文档不存在
        AuthorizationException: 没有访问权限

    Note:
        - 返回的信息包含文档的所有字段
        - 包括文件路径、内容类型、创建时间等元数据
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查访问权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not is_member:
            raise AuthorizationException("没有访问权限")

    # 获取文档
    from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud

    document = await document_crud.get_by_id_and_knowledge_base(
        db, id=document_id, knowledge_base_id=knowledge_base_id
    )
    if not document:
        raise ResourceNotFoundException("文档不存在")

    return success_response(data=document, msg="获取文档详情成功")


@router.delete(
    "/{knowledge_base_id}/documents/{document_id}",
    summary="删除知识库文档",
    description="从指定的知识库中删除一个文档及其所有相关的分块数据。需要相应的编辑权限。",
)
async def delete_knowledge_base_document(
    knowledge_base_id: int,
    document_id: int,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    删除知识库文档

    从指定的知识库中删除一个文档及其所有相关的分块数据。
    需要相应的编辑权限（所有者或编辑者）。

    Args:
        knowledge_base_id: 知识库ID
        document_id: 文档ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含已删除文档信息的成功响应

    Raises:
        ResourceNotFoundException: 知识库或文档不存在
        AuthorizationException: 没有删除文档权限

    Warning:
        - 此操作不可逆，会删除文档及其所有分块数据
        - 同时会从搜索引擎中移除相关索引
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查编辑权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为编辑者成员
        member_role = await knowledge_base_crud.get_member_role(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not member_role or member_role not in ["owner", "editor"]:
            raise AuthorizationException("没有删除文档的权限")

    # 检查文档是否存在
    from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud

    document = await document_crud.get_by_id_and_knowledge_base(
        db, id=document_id, knowledge_base_id=knowledge_base_id
    )
    if not document:
        raise ResourceNotFoundException("文档不存在")

    # 删除文档及其分块
    document_service = KnowledgeBaseDocumentService()
    await document_service.delete_document_and_chunks(db, document_id=document_id)

    return success_response(data=document, msg="文档删除成功")


@router.get(
    "/{knowledge_base_id}/documents/options",
    summary="获取知识库文档选项列表",
    description="获取指定知识库中所有文档的简化列表（ID和名称），用于下拉菜单等场景。需要相应的访问权限。",
)
async def list_knowledge_base_document_options(
    knowledge_base_id: int,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取知识库文档选项列表

    获取指定知识库中所有文档的简化列表（ID和文件名），
    主要用于下拉菜单、选择器等UI组件场景。

    Args:
        knowledge_base_id: 知识库ID
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含文档选项列表的成功响应，每个选项包含id和name字段

    Raises:
        ResourceNotFoundException: 知识库不存在
        AuthorizationException: 没有访问权限

    Note:
        - 获取所有文档，不进行分页
        - 返回格式为 [{"id": doc_id, "name": filename}, ...]
        - 公开知识库的文档所有人都可以查看选项
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查访问权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not is_member and not knowledge_base.is_public:
            raise AuthorizationException("没有访问权限")

    # 获取文档列表
    from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud

    documents = await document_crud.get_multi_by_knowledge_base(
        db,
        knowledge_base_id=knowledge_base_id,
        skip=0,
        limit=None,  # 获取所有文档
    )

    # 转换为选项格式
    document_options = [{"id": doc.id, "name": doc.filename} for doc in documents]

    return success_response(data=document_options, msg="获取文档选项列表成功")


@router.get(
    "/{knowledge_base_id}/documents/{document_id}/chunks",
    summary="获取文档分块列表",
    description="获取指定知识库中特定文档的所有分块内容。需要相应的访问权限。",
)
async def list_document_chunks(
    knowledge_base_id: int,
    document_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_session),
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """
    获取文档分块列表

    获取指定知识库中特定文档的所有分块内容，用于查看文档的详细分块信息。
    需要相应的访问权限。

    Args:
        knowledge_base_id: 知识库ID
        document_id: 文档ID
        skip: 跳过的记录数，用于分页，默认为0
        limit: 返回的记录数，用于分页，默认为100
        db: 数据库会话对象
        current_user_id: 当前用户ID，用于权限验证

    Returns:
        包含文档分块列表的成功响应，每个分块包含内容和元数据

    Raises:
        ResourceNotFoundException: 知识库或文档不存在
        AuthorizationException: 没有访问权限

    Note:
        - 分块按照在文档中的顺序返回
        - 每个分块包含文本内容、位置信息等
        - 支持分页查询以处理大文档
    """
    # 检查知识库是否存在
    knowledge_base = await knowledge_base_crud.get(db, id=knowledge_base_id)
    if not knowledge_base:
        raise ResourceNotFoundException("知识库不存在")

    # 检查访问权限
    if knowledge_base.owner_id != current_user_id:
        # 检查是否为成员
        is_member = await knowledge_base_crud.is_member(
            db, knowledge_base_id=knowledge_base_id, user_id=current_user_id
        )
        if not is_member:
            raise AuthorizationException("没有访问权限")

    # 检查文档是否存在
    from app.infrastructure.database.repositories.crud_knowledge_base import document as document_crud

    document = await document_crud.get_by_id_and_knowledge_base(
        db, id=document_id, knowledge_base_id=knowledge_base_id
    )
    if not document:
        raise ResourceNotFoundException("文档不存在")

    # 获取文档分块
    document_service = KnowledgeBaseDocumentService()
    chunks = await document_service.get_document_chunks(
        db, document_id=document_id, skip=skip, limit=limit
    )

    return success_response(data=chunks, msg="获取文档分块列表成功")
