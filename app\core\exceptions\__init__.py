from app.core.exceptions.base import (
    BaseException,
    ErrorCode,
    ErrorSeverity,
    ErrorContext,
    ErrorDetail,
)

from app.core.exceptions.business import (
    ValidationException,
    AuthenticationException,
    AuthorizationException,
    ResourceNotFoundException,
    ResourceConflictException,
    BusinessException,
    DatabaseException,
    ExternalServiceException,
    RateLimitException,
    CacheException,
    NetworkException,
    TimeoutException,
)

from app.core.exceptions.handlers import (
    exception_handler,
    biohub_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    sqlalchemy_exception_handler,
    general_exception_handler,
)

from app.core.exceptions.utils import (
    handle_exceptions,
    validate_required,
    validate_field_length,
    validate_email,
    validate_phone,
    check_permission,
    check_resource_exists,
    transaction_context,
    retry_on_failure,
    ExceptionCollector,
)

# 向后兼容的异常类名
BioHubException = BaseException

__all__ = [
    # 基础异常
    "BaseException",
    "BioHubException",
    "ErrorCode",
    "ErrorSeverity",
    "ErrorContext",
    "ErrorDetail",
    
    # 业务异常
    "ValidationException",
    "AuthenticationException",
    "AuthorizationException",
    "ResourceNotFoundException",
    "ResourceConflictException",
    "BusinessException",
    "DatabaseException",
    "ExternalServiceException",
    "RateLimitException",
    "CacheException",
    "NetworkException",
    "TimeoutException",
    
    # 异常处理器
    "exception_handler",
    "biohub_exception_handler",
    "http_exception_handler",
    "validation_exception_handler",
    "sqlalchemy_exception_handler",
    "general_exception_handler",
    
    # 工具函数
    "handle_exceptions",
    "validate_required",
    "validate_field_length",
    "validate_email",
    "validate_phone",
    "check_permission",
    "check_resource_exists",
    "transaction_context",
    "retry_on_failure",
    "ExceptionCollector",
]