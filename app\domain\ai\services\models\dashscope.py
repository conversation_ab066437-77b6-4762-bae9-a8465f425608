from dataclasses import dataclass
from typing import Optional, Union

import httpx
from agno.models.openai.chat import OpenAIChat


@dataclass
class BailianLLM(OpenAIChat):
    """
    A class for to interact with any provider using the OpenAI API schema.

    Args:
        id (str): The id of the OpenAI model to use. Defaults to "not-provided".
        name (str): The name of the OpenAI model to use. Defaults to "OpenAILike".
        api_key (Optional[str]): The API key to use. Defaults to "not-provided".
    """

    id: str = "not-provided"
    name: str = "BailianLLM"
    api_key: Optional[str] = "not-provided"
    base_url: Optional[Union[str, httpx.URL]] = (
        "https://dashscope.aliyuncs.com/compatible-mode/v1"
    )

    role_map = {
        "system": "system",
        "user": "user",
        "assistant": "assistant",
        "tool": "tool",
    }
