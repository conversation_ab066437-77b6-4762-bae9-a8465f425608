# AI-BioHub Project

基于FastAPI构建的智能生物信息知识库平台，提供AI驱动的知识检索和管理服务。

## 核心特性

- 🧬 **智能知识库** - 支持多种生物信息文档的上传、索引和检索
- 🔍 **智能搜索** - 基于向量相似度和语义搜索的多维搜索引擎
- 🤖 **AI集成** - 集成DashScope API进行智能问答和内容分析
- 💾 **多数据库支持** - 同时支持PostgreSQL和MySQL数据库
- ⚡ **高性能API** - 基于FastAPI框架的异步API服务
- 🐳 **容器化部署** - 完整的Docker容器化部署方案
- 📊 **监控告警** - 集成Prometheus和Grafana监控体系

## 技术栈

- **框架**: FastAPI 0.115+
- **数据库**: PostgreSQL 15, MySQL 8.0
- **搜索引擎**: Elasticsearch 8.8
- **缓存**: Redis 7
- **AI服务**: DashScope API
- **容器化**: Docker, Docker Compose
- **监控**: Prometheus, Grafana, Loki
- **Web服务器**: Nginx

## 项目结构

```
app/
├── api/                    # API接口层
│   ├── v1/                # API v1版本路由
│   ├── middleware/        # 中间件
│   └── responses.py       # 响应处理
├── core/                  # 核心组件
│   ├── config/           # 配置管理
│   ├── container/        # 依赖注入容器
│   ├── exceptions/       # 异常处理
│   ├── logging/          # 日志系统
│   └── security/         # 安全组件
├── domain/               # 领域层
│   ├── ai/              # AI服务领域
│   └── knowledge_base/  # 知识库领域
├── infrastructure/      # 基础设施层
│   ├── database/       # 数据库
│   ├── external_services/ # 外部服务
│   └── storage/        # 存储服务
└── shared/             # 共享组件
    ├── schemas/        # 数据模型
    ├── types.py        # 类型定义
    ├── docs.py         # 文档工具
    └── utils/          # 工具函数
```

## 快速开始

### 前置要求

- Docker 20.0+
- Docker Compose 2.0+
- Python 3.13+ (本地开发)

### 开发环境部署

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-biohub
```

2. **配置环境变量**
```bash
cp .env.development.example .env
# 编辑 .env 文件填入相应的配置信息
```

3. **一键部署**
```bash
./deploy.sh deploy
```

4. **访问服务**
- API文档: http://localhost/docs
- 健康检查: http://localhost/api/v1/health
- Grafana监控: http://localhost:3000 (admin/admin)

### 生产环境部署

1. **配置生产环境**
```bash
cp .env.prod.example .env
# 编辑 .env 文件填入生产环境配置
```

2. **部署到生产环境**
```bash
./deploy.sh deploy prod
```

## 开发指南

### 本地开发

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动开发服务器**
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

3. **运行测试**
```bash
pytest tests/
```

### API使用示例

#### 创建知识库
```bash
curl -X POST "http://localhost:8000/api/v1/knowledge_base/create" \
     -H "Content-Type: application/json" \
     -d '{"name": "生物信息知识库", "description": "用于存储生物信息相关文档"}'
```

#### 上传文档
```bash
curl -X POST "http://localhost:8000/api/v1/knowledge_base/1/upload_document" \
     -F "file=@document.pdf"
```

#### 搜索文档
```bash
curl -X GET "http://localhost:8000/api/v1/knowledge_base/1/search?query=基因表达分析"
```

## 部署管理

项目提供了完整的部署脚本 `deploy.sh`：

```bash
# 构建镜像
./deploy.sh build [prod]

# 启动服务
./deploy.sh start [prod]

# 停止服务
./deploy.sh stop [prod]

# 查看状态
./deploy.sh status [prod]

# 查看日志
./deploy.sh logs <service> [prod]

# 完整部署
./deploy.sh deploy [prod]

# 健康检查
./deploy.sh health [prod]

# 数据备份
./deploy.sh backup [prod]
```

## 配置说明

### 环境变量配置

主要配置项说明：

- `APP__ENVIRONMENT`: 运行环境 (development/production)
- `DATABASE__POSTGRES_*`: PostgreSQL数据库配置
- `DATABASE__MYSQL_*`: MySQL数据库配置
- `REDIS_*`: Redis缓存配置
- `ELASTICSEARCH_*`: Elasticsearch搜索引擎配置
- `DASHSCOPE_API_KEY`: DashScope AI服务API密钥
- `SECURITY__JWT_SECRET_KEY`: JWT密钥

### Docker Compose服务

- `app`: 主应用服务
- `postgres`: PostgreSQL数据库
- `mysql`: MySQL数据库
- `redis`: Redis缓存
- `elasticsearch`: Elasticsearch搜索引擎
- `nginx`: Web服务器和反向代理
- `grafana`: 监控面板
- `prometheus`: 监控数据收集
- `loki`: 日志聚合

## 监控运维

### Grafana监控面板

访问 http://localhost:3000 查看监控面板：
- 系统性能指标
- 数据库连接状态
- API请求统计
- 错误率和延迟分布

### 日志查看

```bash
# 查看应用日志
./deploy.sh logs app

# 查看数据库日志
./deploy.sh logs postgres

# 查看所有服务日志
docker-compose logs -f
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否正常启动
   - 验证连接配置是否正确
   - 查看数据库日志

2. **Elasticsearch连接异常**
   - 确认Elasticsearch服务状态正常
   - 检查索引创建是否成功
   - 验证API密钥配置

3. **应用启动失败**
   - 检查环境变量配置
   - 查看应用启动日志排查具体错误
   - 确认依赖服务已正常启动

### 性能优化

- 合理配置数据库连接池大小
- 配置Redis缓存提高响应速度
- 优化Elasticsearch索引策略
- 启用Nginx缓存

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目使用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详细信息。

## 联系信息

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目地址: [https://github.com/yourorg/ai-biohub](https://github.com/yourorg/ai-biohub)