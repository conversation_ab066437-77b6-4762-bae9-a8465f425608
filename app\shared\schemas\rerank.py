from typing import List, Optional
from pydantic import BaseModel, Field


class RerankDocument(BaseModel):
    """重排序文档模型"""
    
    text: str = Field(description="文档文本内容")
    
    class Config:
        json_schema_extra = {
            "example": {
                "text": "人工智能技术正在快速发展，深度学习是其核心技术之一。"
            }
        }


class RerankResult(BaseModel):
    """单个重排序结果模型"""
    
    index: int = Field(description="文档在原始列表中的索引")
    relevance_score: float = Field(description="相关性分数，范围通常在0-1之间")
    document: Optional[RerankDocument] = Field(default=None, description="文档内容（当return_documents=True时返回）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "index": 0,
                "relevance_score": 0.95,
                "document": {
                    "text": "人工智能技术正在快速发展，深度学习是其核心技术之一。"
                }
            }
        }


class RerankUsage(BaseModel):
    """重排序API使用统计模型"""
    
    total_tokens: int = Field(description="总token消耗数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_tokens": 150
            }
        }


class RerankOutput(BaseModel):
    """重排序输出数据模型"""
    
    results: List[RerankResult] = Field(description="重排序结果列表，按相关性分数降序排列")
    
    class Config:
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "index": 0,
                        "relevance_score": 0.95,
                        "document": {
                            "text": "人工智能技术正在快速发展，深度学习是其核心技术之一。"
                        }
                    },
                    {
                        "index": 2,
                        "relevance_score": 0.88,
                        "document": {
                            "text": "自然语言处理是人工智能的重要分支。"
                        }
                    }
                ]
            }
        }


class RerankApiResponse(BaseModel):
    """DashScope重排序API完整响应模型"""
    
    output: RerankOutput = Field(description="重排序输出数据")
    usage: RerankUsage = Field(description="API使用统计")
    request_id: str = Field(description="请求ID，用于追踪和调试")
    
    class Config:
        json_schema_extra = {
            "example": {
                "output": {
                    "results": [
                        {
                            "index": 0,
                            "relevance_score": 0.95,
                            "document": {
                                "text": "人工智能技术正在快速发展，深度学习是其核心技术之一。"
                            }
                        },
                        {
                            "index": 2,
                            "relevance_score": 0.88,
                            "document": {
                                "text": "自然语言处理是人工智能的重要分支。"
                            }
                        }
                    ]
                },
                "usage": {
                    "total_tokens": 150
                },
                "request_id": "12345678-1234-1234-1234-123456789abc"
            }
        }


class RerankRequest(BaseModel):
    """重排序请求模型"""
    
    query: str = Field(description="查询文本")
    documents: List[str] = Field(description="待排序的文档列表")
    model: str = Field(default="gte-rerank-v2", description="使用的模型")
    top_n: int = Field(default=5, ge=1, le=100, description="返回的排序结果数量")
    return_documents: bool = Field(default=True, description="是否在结果中返回文档内容")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "人工智能的发展趋势",
                "documents": [
                    "人工智能技术正在快速发展，深度学习是其核心技术之一。",
                    "机器学习算法在各个领域都有广泛应用。",
                    "自然语言处理是人工智能的重要分支。"
                ],
                "model": "gte-rerank-v2",
                "top_n": 5,
                "return_documents": True
            }
        }


class RerankServiceResponse(BaseModel):
    """重排序服务响应模型"""
    
    query: str = Field(description="原始查询文本")
    results: List[RerankResult] = Field(description="重排序结果列表")
    total_results: int = Field(description="返回的结果总数")
    model_used: str = Field(description="使用的模型名称")
    request_id: str = Field(description="请求ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "人工智能的发展趋势",
                "results": [
                    {
                        "index": 0,
                        "relevance_score": 0.95,
                        "document": {
                            "text": "人工智能技术正在快速发展，深度学习是其核心技术之一。"
                        }
                    }
                ],
                "total_results": 1,
                "model_used": "gte-rerank-v2",
                "request_id": "12345678-1234-1234-1234-123456789abc"
            }
        }