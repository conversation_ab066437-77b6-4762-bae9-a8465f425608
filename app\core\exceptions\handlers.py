from typing import Any, Dict, List, Optional, Type
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.core.exceptions.base import BaseException, ErrorContext, ErrorCode, ErrorSeverity
from app.core.exceptions.business import (
    ValidationException,
    DatabaseException,
    ExternalServiceException,
    TimeoutException,
    NetworkException
)


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("app.exceptions")
        self._handlers: Dict[Type[Exception], callable] = {}
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认异常处理器"""
        self._handlers[BaseException] = self._handle_base_exception
        self._handlers[StarletteHTTPException] = self._handle_http_exception
        self._handlers[RequestValidationError] = self._handle_validation_error
        self._handlers[SQLAlchemyError] = self._handle_database_error
        self._handlers[Exception] = self._handle_generic_exception
    
    def register_handler(self, exception_type: Type[Exception], handler: callable):
        """注册自定义异常处理器"""
        self._handlers[exception_type] = handler
    
    def _create_error_context(self, request: Request) -> ErrorContext:
        """创建错误上下文"""
        return ErrorContext(
            request_id=getattr(request.state, "request_id", "unknown"),
            user_id=getattr(request.state, "user_id", None),
            session_id=getattr(request.state, "session_id", None),
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("User-Agent"),
            path=request.url.path,
            method=request.method,
        )
    
    def _log_exception(self, exception: BaseException, context: ErrorContext):
        """记录异常日志"""
        if not exception.should_log:
            return
        
        log_data = {
            "error_id": exception.error_id,
            "error_code": exception.code.value,
            "request_id": context.request_id,
            "user_id": context.user_id,
            "path": context.path,
            "method": context.method,
            "severity": exception.severity.value,
            "details": [d.__dict__ for d in exception.details],
            "extra_data": exception.extra_data,
        }
        
        if exception.severity == ErrorSeverity.LOW:
            self.logger.warning(f"[{exception.code.value}] {exception.message}", extra=log_data)
        elif exception.severity == ErrorSeverity.MEDIUM:
            self.logger.error(f"[{exception.code.value}] {exception.message}", extra=log_data)
        elif exception.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.critical(f"[{exception.code.value}] {exception.message}", extra=log_data, exc_info=True)
    
    def _handle_base_exception(self, request: Request, exception: BaseException) -> JSONResponse:
        """处理基础异常"""
        context = self._create_error_context(request)
        exception.set_context(context)
        
        # 记录日志
        self._log_exception(exception, context)
        
        # 构建响应
        response_data = exception.to_dict()
        
        # 添加调试信息（仅在开发环境）
        from app.core.config.settings import settings
        if settings.app.DEBUG:
            response_data["debug"] = {
                "traceback": exception.traceback,
                "extra_data": exception.extra_data,
            }
        
        return JSONResponse(
            status_code=exception.http_status,
            content=response_data,
            headers={"X-Error-ID": exception.error_id}
        )
    
    def _handle_http_exception(self, request: Request, exception: StarletteHTTPException) -> JSONResponse:
        """处理HTTP异常"""
        context = self._create_error_context(request)
        
        # 转换为基础异常
        base_exception = BaseException(
            message=str(exception.detail),
            code=self._get_error_code_from_status(exception.status_code),
            http_status=exception.status_code,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )
        
        return self._handle_base_exception(request, base_exception)
    
    def _handle_validation_error(self, request: Request, exception: RequestValidationError) -> JSONResponse:
        """处理验证错误"""
        context = self._create_error_context(request)
        
        # 解析验证错误
        field_errors = {}
        for error in exception.errors():
            field = ".".join(str(loc) for loc in error["loc"])
            field_errors[field] = error["msg"]
        
        # 转换为验证异常
        validation_exception = ValidationException(
            message="请求参数验证失败",
            field_errors=field_errors,
            context=context,
        )
        
        return self._handle_base_exception(request, validation_exception)
    
    def _handle_database_error(self, request: Request, exception: SQLAlchemyError) -> JSONResponse:
        """处理数据库错误"""
        context = self._create_error_context(request)
        
        db_exception = DatabaseException(
            message=f"数据库操作失败: {str(exception)}",
            context=context,
        )
        
        return self._handle_base_exception(request, db_exception)
    
    def _handle_generic_exception(self, request: Request, exception: Exception) -> JSONResponse:
        """处理通用异常"""
        context = self._create_error_context(request)
        
        # 根据异常类型进行分类
        if "timeout" in str(exception).lower():
            typed_exception = TimeoutException(
                message=f"操作超时: {str(exception)}",
                context=context,
            )
        elif "connection" in str(exception).lower():
            typed_exception = NetworkException(
                message=f"网络连接失败: {str(exception)}",
                context=context,
            )
        else:
            typed_exception = BaseException(
                message=f"未知错误: {str(exception)}",
                code=ErrorCode.INTERNAL_SERVER_ERROR,
                http_status=500,
                severity=ErrorSeverity.CRITICAL,
                context=context,
            )
        
        return self._handle_base_exception(request, typed_exception)
    
    def _get_error_code_from_status(self, status_code: int) -> ErrorCode:
        """根据HTTP状态码获取错误码"""
        mapping = {
            400: ErrorCode.BAD_REQUEST,
            401: ErrorCode.UNAUTHORIZED,
            403: ErrorCode.FORBIDDEN,
            404: ErrorCode.NOT_FOUND,
            405: ErrorCode.METHOD_NOT_ALLOWED,
            422: ErrorCode.VALIDATION_ERROR,
            429: ErrorCode.RATE_LIMIT_EXCEEDED,
            500: ErrorCode.INTERNAL_SERVER_ERROR,
            503: ErrorCode.SERVICE_UNAVAILABLE,
        }
        return mapping.get(status_code, ErrorCode.INTERNAL_SERVER_ERROR)
    
    async def handle_exception(self, request: Request, exception: Exception) -> JSONResponse:
        """处理异常"""
        # 查找最匹配的处理器
        handler = None
        for exc_type, exc_handler in self._handlers.items():
            if isinstance(exception, exc_type):
                handler = exc_handler
                break
        
        if handler is None:
            handler = self._handle_generic_exception
        
        return handler(request, exception)


# 全局异常处理器实例
exception_handler = ExceptionHandler()


# 便利函数
async def biohub_exception_handler(request: Request, exc: BaseException) -> JSONResponse:
    """BioHub异常处理器"""
    return await exception_handler.handle_exception(request, exc)


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    return await exception_handler.handle_exception(request, exc)


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """验证异常处理器"""
    return await exception_handler.handle_exception(request, exc)


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """SQLAlchemy异常处理器"""
    return await exception_handler.handle_exception(request, exc)


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    return await exception_handler.handle_exception(request, exc)