import logging
import logging.handlers
from typing import Dict

from app.core.container.container import Container, ServiceProvider
from app.core.config.settings import settings


class LoggingServiceProvider(ServiceProvider):
    """日志服务提供者"""
    
    def register(self, container: Container) -> None:
        """注册日志服务"""
        
        def create_logger_factory():
            """创建日志工厂"""
            loggers: Dict[str, logging.Logger] = {}
            
            def get_logger(name: str) -> logging.Logger:
                if name not in loggers:
                    logger = logging.getLogger(name)
                    logger.setLevel(getattr(logging, settings.logging.LOG_LEVEL))
                    
                    # 避免重复添加处理器
                    if not logger.handlers:
                        # 文件处理器
                        file_handler = logging.handlers.RotatingFileHandler(
                            settings.logging.LOG_FILE,
                            maxBytes=settings.logging.LOG_MAX_SIZE,
                            backupCount=settings.logging.LOG_BACKUP_COUNT,
                            encoding='utf-8'
                        )
                        file_handler.setFormatter(logging.Formatter(
                            settings.logging.LOG_FORMAT,
                            datefmt=settings.logging.DATE_FORMAT
                        ))
                        logger.addHandler(file_handler)
                        
                        # 错误日志处理器
                        error_handler = logging.handlers.RotatingFileHandler(
                            settings.logging.ERROR_LOG_FILE,
                            maxBytes=settings.logging.ERROR_LOG_MAX_SIZE,
                            backupCount=settings.logging.ERROR_LOG_BACKUP_COUNT,
                            encoding='utf-8'
                        )
                        error_handler.setLevel(logging.ERROR)
                        error_handler.setFormatter(logging.Formatter(
                            settings.logging.LOG_FORMAT,
                            datefmt=settings.logging.DATE_FORMAT
                        ))
                        logger.addHandler(error_handler)
                        
                        # 控制台处理器
                        if settings.logging.CONSOLE_LOG_ENABLED:
                            console_handler = logging.StreamHandler()
                            console_handler.setLevel(getattr(logging, settings.logging.CONSOLE_LOG_LEVEL))
                            console_handler.setFormatter(logging.Formatter(
                                settings.logging.LOG_FORMAT,
                                datefmt=settings.logging.DATE_FORMAT
                            ))
                            logger.addHandler(console_handler)
                    
                    loggers[name] = logger
                
                return loggers[name]
            
            return get_logger
        
        # 注册日志工厂
        container.singleton("logger_factory", create_logger_factory)
        
        # 注册默认日志器
        def create_default_logger():
            logger_factory = container.get("logger_factory")
            return logger_factory("app")
        
        container.singleton("logger", create_default_logger)