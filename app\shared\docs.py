"""
文档标准和注释规范
定义项目中统一的文档编写标准
"""

from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, Type
from enum import Enum
from dataclasses import dataclass
from functools import wraps
import inspect


class DocstringStyle(Enum):
    """文档字符串风格"""
    GOOGLE = "google"
    NUMPY = "numpy"
    SPHINX = "sphinx"


@dataclass
class DocstringSection:
    """文档字符串段落"""
    name: str
    content: str
    required: bool = False


class DocstringTemplate:
    """文档字符串模板"""
    
    @staticmethod
    def function_docstring(
        description: str,
        args: Optional[Dict[str, str]] = None,
        returns: Optional[str] = None,
        raises: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None,
        style: DocstringStyle = DocstringStyle.GOOGLE
    ) -> str:
        """
        生成函数文档字符串
        
        Args:
            description: 函数描述
            args: 参数说明字典，格式为 {参数名: 说明}
            returns: 返回值说明
            raises: 异常说明字典，格式为 {异常类型: 说明}
            examples: 使用示例列表
            notes: 注意事项列表
            style: 文档风格
        
        Returns:
            str: 格式化的文档字符串
        
        Examples:
            >>> template = DocstringTemplate()
            >>> doc = template.function_docstring(
            ...     description="计算两个数的和",
            ...     args={"a": "第一个数", "b": "第二个数"},
            ...     returns="两个数的和"
            ... )
            >>> print(doc)
        """
        if style == DocstringStyle.GOOGLE:
            return DocstringTemplate._google_style_function(
                description, args, returns, raises, examples, notes
            )
        elif style == DocstringStyle.NUMPY:
            return DocstringTemplate._numpy_style_function(
                description, args, returns, raises, examples, notes
            )
        else:
            return DocstringTemplate._sphinx_style_function(
                description, args, returns, raises, examples, notes
            )
    
    @staticmethod
    def class_docstring(
        description: str,
        attributes: Optional[Dict[str, str]] = None,
        methods: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None,
        style: DocstringStyle = DocstringStyle.GOOGLE
    ) -> str:
        """
        生成类文档字符串
        
        Args:
            description: 类描述
            attributes: 属性说明字典
            methods: 方法说明字典
            examples: 使用示例列表
            notes: 注意事项列表
            style: 文档风格
        
        Returns:
            str: 格式化的类文档字符串
        """
        if style == DocstringStyle.GOOGLE:
            return DocstringTemplate._google_style_class(
                description, attributes, methods, examples, notes
            )
        elif style == DocstringStyle.NUMPY:
            return DocstringTemplate._numpy_style_class(
                description, attributes, methods, examples, notes
            )
        else:
            return DocstringTemplate._sphinx_style_class(
                description, attributes, methods, examples, notes
            )
    
    @staticmethod
    def _google_style_function(
        description: str,
        args: Optional[Dict[str, str]] = None,
        returns: Optional[str] = None,
        raises: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """Google风格函数文档"""
        lines = [description]
        
        if args:
            lines.append("")
            lines.append("Args:")
            for arg_name, arg_desc in args.items():
                lines.append(f"    {arg_name}: {arg_desc}")
        
        if returns:
            lines.append("")
            lines.append("Returns:")
            lines.append(f"    {returns}")
        
        if raises:
            lines.append("")
            lines.append("Raises:")
            for exc_type, exc_desc in raises.items():
                lines.append(f"    {exc_type}: {exc_desc}")
        
        if examples:
            lines.append("")
            lines.append("Examples:")
            for example in examples:
                lines.append(f"    {example}")
        
        if notes:
            lines.append("")
            lines.append("Note:")
            for note in notes:
                lines.append(f"    {note}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _google_style_class(
        description: str,
        attributes: Optional[Dict[str, str]] = None,
        methods: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """Google风格类文档"""
        lines = [description]
        
        if attributes:
            lines.append("")
            lines.append("Attributes:")
            for attr_name, attr_desc in attributes.items():
                lines.append(f"    {attr_name}: {attr_desc}")
        
        if methods:
            lines.append("")
            lines.append("Methods:")
            for method_name, method_desc in methods.items():
                lines.append(f"    {method_name}: {method_desc}")
        
        if examples:
            lines.append("")
            lines.append("Examples:")
            for example in examples:
                lines.append(f"    {example}")
        
        if notes:
            lines.append("")
            lines.append("Note:")
            for note in notes:
                lines.append(f"    {note}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _numpy_style_function(
        description: str,
        args: Optional[Dict[str, str]] = None,
        returns: Optional[str] = None,
        raises: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """NumPy风格函数文档"""
        lines = [description]
        
        if args:
            lines.append("")
            lines.append("Parameters")
            lines.append("----------")
            for arg_name, arg_desc in args.items():
                lines.append(f"{arg_name} : type")
                lines.append(f"    {arg_desc}")
        
        if returns:
            lines.append("")
            lines.append("Returns")
            lines.append("-------")
            lines.append(f"type")
            lines.append(f"    {returns}")
        
        if raises:
            lines.append("")
            lines.append("Raises")
            lines.append("------")
            for exc_type, exc_desc in raises.items():
                lines.append(f"{exc_type}")
                lines.append(f"    {exc_desc}")
        
        if examples:
            lines.append("")
            lines.append("Examples")
            lines.append("--------")
            for example in examples:
                lines.append(example)
        
        if notes:
            lines.append("")
            lines.append("Notes")
            lines.append("-----")
            for note in notes:
                lines.append(note)
        
        return "\n".join(lines)
    
    @staticmethod
    def _numpy_style_class(
        description: str,
        attributes: Optional[Dict[str, str]] = None,
        methods: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """NumPy风格类文档"""
        lines = [description]
        
        if attributes:
            lines.append("")
            lines.append("Attributes")
            lines.append("----------")
            for attr_name, attr_desc in attributes.items():
                lines.append(f"{attr_name} : type")
                lines.append(f"    {attr_desc}")
        
        if methods:
            lines.append("")
            lines.append("Methods")
            lines.append("-------")
            for method_name, method_desc in methods.items():
                lines.append(f"{method_name}")
                lines.append(f"    {method_desc}")
        
        if examples:
            lines.append("")
            lines.append("Examples")
            lines.append("--------")
            for example in examples:
                lines.append(example)
        
        if notes:
            lines.append("")
            lines.append("Notes")
            lines.append("-----")
            for note in notes:
                lines.append(note)
        
        return "\n".join(lines)
    
    @staticmethod
    def _sphinx_style_function(
        description: str,
        args: Optional[Dict[str, str]] = None,
        returns: Optional[str] = None,
        raises: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """Sphinx风格函数文档"""
        lines = [description]
        
        if args:
            lines.append("")
            for arg_name, arg_desc in args.items():
                lines.append(f":param {arg_name}: {arg_desc}")
        
        if returns:
            lines.append(f":return: {returns}")
        
        if raises:
            lines.append("")
            for exc_type, exc_desc in raises.items():
                lines.append(f":raises {exc_type}: {exc_desc}")
        
        if examples:
            lines.append("")
            lines.append(".. code-block:: python")
            lines.append("")
            for example in examples:
                lines.append(f"   {example}")
        
        if notes:
            lines.append("")
            lines.append(".. note::")
            for note in notes:
                lines.append(f"   {note}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _sphinx_style_class(
        description: str,
        attributes: Optional[Dict[str, str]] = None,
        methods: Optional[Dict[str, str]] = None,
        examples: Optional[List[str]] = None,
        notes: Optional[List[str]] = None
    ) -> str:
        """Sphinx风格类文档"""
        lines = [description]
        
        if attributes:
            lines.append("")
            for attr_name, attr_desc in attributes.items():
                lines.append(f":ivar {attr_name}: {attr_desc}")
        
        if methods:
            lines.append("")
            for method_name, method_desc in methods.items():
                lines.append(f":method {method_name}: {method_desc}")
        
        if examples:
            lines.append("")
            lines.append(".. code-block:: python")
            lines.append("")
            for example in examples:
                lines.append(f"   {example}")
        
        if notes:
            lines.append("")
            lines.append(".. note::")
            for note in notes:
                lines.append(f"   {note}")
        
        return "\n".join(lines)


def documented(
    description: str,
    args: Optional[Dict[str, str]] = None,
    returns: Optional[str] = None,
    raises: Optional[Dict[str, str]] = None,
    examples: Optional[List[str]] = None,
    notes: Optional[List[str]] = None,
    style: DocstringStyle = DocstringStyle.GOOGLE
):
    """
    文档装饰器，自动生成标准化的文档字符串
    
    Args:
        description: 函数描述
        args: 参数说明字典
        returns: 返回值说明
        raises: 异常说明字典
        examples: 使用示例列表
        notes: 注意事项列表
        style: 文档风格
    
    Returns:
        Callable: 装饰后的函数
    
    Examples:
        >>> @documented(
        ...     description="计算两个数的和",
        ...     args={"a": "第一个数", "b": "第二个数"},
        ...     returns="两个数的和"
        ... )
        ... def add(a: int, b: int) -> int:
        ...     return a + b
    """
    def decorator(func: Callable) -> Callable:
        # 获取函数签名
        sig = inspect.signature(func)
        
        # 如果没有提供args，尝试从类型注解中提取
        if args is None:
            extracted_args = {}
            for param_name, param in sig.parameters.items():
                if param_name != 'self':
                    extracted_args[param_name] = f"{param_name} parameter"
        else:
            extracted_args = args
        
        # 生成文档字符串
        docstring = DocstringTemplate.function_docstring(
            description=description,
            args=extracted_args,
            returns=returns,
            raises=raises,
            examples=examples,
            notes=notes,
            style=style
        )
        
        # 设置文档字符串
        func.__doc__ = docstring
        
        return func
    
    return decorator


def validate_docstring(func: Callable) -> List[str]:
    """
    验证函数文档字符串的完整性
    
    Args:
        func: 要验证的函数
    
    Returns:
        List[str]: 验证结果，空列表表示验证通过
    
    Examples:
        >>> def example_func(a: int, b: str) -> bool:
        ...     '''简单的示例函数'''
        ...     return True
        >>> issues = validate_docstring(example_func)
        >>> print(issues)
        ['Missing parameter documentation for: a, b', 'Missing return documentation']
    """
    issues = []
    
    # 检查是否有文档字符串
    if not func.__doc__:
        issues.append("Missing docstring")
        return issues
    
    # 获取函数签名
    sig = inspect.signature(func)
    
    # 检查参数文档
    doc = func.__doc__
    documented_params = []
    
    # 简单的参数文档检查（实际实现可能需要更复杂的解析）
    for param_name, param in sig.parameters.items():
        if param_name != 'self' and param_name not in doc:
            documented_params.append(param_name)
    
    if documented_params:
        issues.append(f"Missing parameter documentation for: {', '.join(documented_params)}")
    
    # 检查返回值文档
    if sig.return_annotation != inspect.Signature.empty:
        if "Returns:" not in doc and "return" not in doc.lower():
            issues.append("Missing return documentation")
    
    return issues


# 常用文档模板
COMMON_DOCSTRING_TEMPLATES = {
    'repository_get': {
        'description': '根据ID获取{entity}',
        'args': {'id': '{entity}的唯一标识符'},
        'returns': '找到的{entity}对象，如果不存在则返回None',
        'raises': {
            'DatabaseException': '数据库查询失败时抛出'
        }
    },
    'repository_create': {
        'description': '创建新的{entity}',
        'args': {'obj_in': '创建{entity}所需的数据'},
        'returns': '创建成功的{entity}对象',
        'raises': {
            'DatabaseException': '数据库操作失败时抛出',
            'ValidationException': '数据验证失败时抛出'
        }
    },
    'service_business_logic': {
        'description': '执行{operation}业务逻辑',
        'args': {'data': '业务操作所需的数据'},
        'returns': '业务操作的结果',
        'raises': {
            'BusinessException': '业务逻辑验证失败时抛出',
            'AuthorizationException': '权限验证失败时抛出'
        }
    },
    'api_endpoint': {
        'description': '{operation} API端点',
        'args': {'request': 'HTTP请求对象'},
        'returns': 'API响应结果',
        'raises': {
            'HTTPException': 'HTTP错误时抛出'
        }
    }
}


def get_template(template_name: str, **kwargs) -> Dict[str, Any]:
    """
    获取文档模板并进行参数替换
    
    Args:
        template_name: 模板名称
        **kwargs: 模板参数
    
    Returns:
        Dict[str, Any]: 替换后的模板数据
    
    Examples:
        >>> template = get_template('repository_get', entity='用户')
        >>> print(template['description'])
        根据ID获取用户
    """
    if template_name not in COMMON_DOCSTRING_TEMPLATES:
        raise ValueError(f"Unknown template: {template_name}")
    
    template = COMMON_DOCSTRING_TEMPLATES[template_name].copy()
    
    # 递归替换所有字符串中的占位符
    def replace_placeholders(obj, replacements):
        if isinstance(obj, str):
            for key, value in replacements.items():
                obj = obj.replace(f'{{{key}}}', str(value))
            return obj
        elif isinstance(obj, dict):
            return {k: replace_placeholders(v, replacements) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [replace_placeholders(item, replacements) for item in obj]
        else:
            return obj
    
    return replace_placeholders(template, kwargs)