import functools
from typing import Any, Callable, Dict, Optional, Type, TypeVar, get_type_hints

from app.core.container.container import container, Container

T = TypeVar("T")


def inject(container_instance: Optional[Container] = None):
    """依赖注入装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            _container = container_instance or container
            
            # 获取函数的类型注解
            type_hints = get_type_hints(func)
            
            # 注入依赖
            for param_name, param_type in type_hints.items():
                if param_name != 'return' and param_name not in kwargs:
                    # 尝试从容器获取依赖
                    service_key = getattr(param_type, '__name__', str(param_type))
                    if _container.has(service_key):
                        kwargs[param_name] = _container.get(service_key)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def singleton(key: Optional[str] = None, container_instance: Optional[Container] = None):
    """单例装饰器"""
    def decorator(cls: Type[T]) -> Type[T]:
        _container = container_instance or container
        service_key = key or cls.__name__
        
        def factory():
            return cls(_container)
        
        _container.singleton(service_key, factory)
        return cls
    return decorator


def service(key: Optional[str] = None, container_instance: Optional[Container] = None):
    """服务装饰器"""
    def decorator(cls: Type[T]) -> Type[T]:
        _container = container_instance or container
        service_key = key or cls.__name__
        
        def factory():
            return cls(_container)
        
        _container.factory(service_key, factory)
        return cls
    return decorator


def injectable(cls: Type[T]) -> Type[T]:
    """可注入类装饰器"""
    original_init = cls.__init__
    
    @functools.wraps(original_init)
    def new_init(self, *args, **kwargs):
        # 获取类型注解
        type_hints = get_type_hints(original_init)
        
        # 注入依赖
        for param_name, param_type in type_hints.items():
            if param_name not in ['self', 'return'] and param_name not in kwargs:
                service_key = getattr(param_type, '__name__', str(param_type))
                if container.has(service_key):
                    kwargs[param_name] = container.get(service_key)
        
        original_init(self, *args, **kwargs)
    
    cls.__init__ = new_init
    return cls


def depends(key: str, container_instance: Optional[Container] = None):
    """依赖注入参数装饰器"""
    def get_dependency():
        _container = container_instance or container
        return _container.get(key)
    
    return get_dependency