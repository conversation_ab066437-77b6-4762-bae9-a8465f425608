# 操作系统和IDE文件
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development
.env.production
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# 项目特定文件
logs/
uploads/
static/uploaded/
*.db
*.sqlite

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 配置文件（包含敏感信息）
config.ini
secrets.yml
.secrets

# Docker
.dockerignore

# 数据库文件
*.sql
*.dump

# 缓存文件
.cache/
*.cache

# 备份文件
*.bak
*.backup

# 编译文件
*.pyc
*.pyo
*.pyd

# 文档构建输出
docs/build/
docs/dist/

# 本地开发文件
.local/
local/

# 性能分析文件
*.prof
*.pstats

# 调试文件
*.debug

# 锁定文件（根据需要决定是否忽略）
# uv.lock
# poetry.lock