# 开发环境配置示例
# 复制此文件并重命名为 .env.development 来使用

# 环境配置
APP__ENVIRONMENT=development
APP__DEBUG=true
APP__HOST=0.0.0.0
APP__PORT=8000
APP__WORKERS=1

# 数据库配置
DATABASE__POSTGRES_SERVER=localhost
DATABASE__POSTGRES_USER=root
DATABASE__POSTGRES_PASSWORD=821206
DATABASE__POSTGRES_DB=ai-biohub
DATABASE__POSTGRES_PORT=5432

DATABASE__MYSQL_SERVER=*********
DATABASE__MYSQL_USER=root
DATABASE__MYSQL_PASSWORD=y5HE!o@o!16
DATABASE__MYSQL_DB=ysh_base
DATABASE__MYSQL_PORT=3306

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Elasticsearch配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_API_KEY=None
ELASTICSEARCH_INDEX_PREFIX=ai-biohub

# AI服务配置
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# 安全配置
SECURITY__AUTH_SERVICE_URL=http://localhost:8080/api
SECURITY__JWT_SECRET_KEY=your-jwt-secret-key-here-at-least-32-characters-long
SECURITY__JWT_ALGORITHM=HS256
SECURITY__JWT_EXPIRE_MINUTES=30

# 日志配置
LOGGING__LOG_LEVEL=DEBUG
LOGGING__LOG_FILE=logs/app.log
LOGGING__CONSOLE_LOG_ENABLED=true
LOGGING__CONSOLE_LOG_LEVEL=DEBUG

# 存储配置
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=104857600  # 100MB