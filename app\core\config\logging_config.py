from typing import Dict, Any, Optional
from pydantic import field_validator

from app.core.config.base import BaseConfig


class LoggingConfig(BaseConfig):
    """日志配置"""
    
    # 基础日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # 错误日志配置
    ERROR_LOG_FILE: str = "logs/app_error.log"
    ERROR_LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    ERROR_LOG_BACKUP_COUNT: int = 5
    
    # 访问日志配置
    ACCESS_LOG_ENABLED: bool = True
    ACCESS_LOG_FILE: str = "logs/access.log"
    ACCESS_LOG_MAX_SIZE: int = 50 * 1024 * 1024  # 50MB
    ACCESS_LOG_BACKUP_COUNT: int = 10
    
    # 日志格式配置
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DATE_FORMAT: str = "%Y-%m-%d %H:%M:%S"
    
    # 控制台日志配置
    CONSOLE_LOG_ENABLED: bool = True
    CONSOLE_LOG_LEVEL: str = "INFO"
    
    # 结构化日志配置
    STRUCTURED_LOGGING: bool = False
    JSON_LOGS: bool = False
    
    @field_validator("LOG_LEVEL", "CONSOLE_LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    @field_validator("LOG_MAX_SIZE", "ERROR_LOG_MAX_SIZE", "ACCESS_LOG_MAX_SIZE")
    def validate_log_size(cls, v):
        if v < 1024:  # 1KB minimum
            raise ValueError("Log file size must be at least 1KB")
        return v
    
    @field_validator("LOG_BACKUP_COUNT", "ERROR_LOG_BACKUP_COUNT", "ACCESS_LOG_BACKUP_COUNT")
    def validate_backup_count(cls, v):
        if v < 0:
            raise ValueError("Backup count must be non-negative")
        return v
    
    def validate_config(self) -> None:
        """验证配置"""
        # 验证日志目录路径
        import os
        for log_file in [self.LOG_FILE, self.ERROR_LOG_FILE, self.ACCESS_LOG_FILE]:
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir, exist_ok=True)
                except OSError as e:
                    raise ValueError(f"Cannot create log directory {log_dir}: {e}")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "log_level": self.LOG_LEVEL,
            "log_file": self.LOG_FILE,
            "log_max_size": self.LOG_MAX_SIZE,
            "log_backup_count": self.LOG_BACKUP_COUNT,
            "access_log_enabled": self.ACCESS_LOG_ENABLED,
            "console_log_enabled": self.CONSOLE_LOG_ENABLED,
            "console_log_level": self.CONSOLE_LOG_LEVEL,
            "structured_logging": self.STRUCTURED_LOGGING,
            "json_logs": self.JSON_LOGS,
        }