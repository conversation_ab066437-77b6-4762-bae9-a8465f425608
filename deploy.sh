#!/bin/bash

# 部署脚本
# 用于快速部署AI-BioHub项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_message "Docker and Docker Compose are installed."
}

# 函数：检查环境文件
check_env_files() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        if [ -f .env.example ]; then
            cp .env.example .env
            print_message "Created .env from .env.example"
        else
            print_error "No .env.example file found. Please create environment configuration."
            exit 1
        fi
    fi
    
    print_message "Environment files are ready."
}

# 函数：构建Docker镜像
build_images() {
    print_message "Building Docker images..."
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml build
    else
        docker-compose build
    fi
    
    print_message "Docker images built successfully."
}

# 函数：启动服务
start_services() {
    print_message "Starting services..."
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    print_message "Services started successfully."
}

# 函数：停止服务
stop_services() {
    print_message "Stopping services..."
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    print_message "Services stopped successfully."
}

# 函数：查看服务状态
check_status() {
    print_message "Checking service status..."
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# 函数：查看日志
view_logs() {
    if [ "$2" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml logs -f $1
    else
        docker-compose logs -f $1
    fi
}

# 函数：初始化数据库
init_database() {
    print_message "Initializing database..."
    
    # 等待数据库启动
    sleep 30
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml exec app python -c "from app.infrastructure.database.base import init_db; init_db()"
    else
        docker-compose exec app python -c "from app.infrastructure.database.base import init_db; init_db()"
    fi
    
    print_message "Database initialized successfully."
}

# 函数：运行迁移
run_migrations() {
    print_message "Running database migrations..."
    
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml exec app python -c "from app.infrastructure.database.tools import migration_tool; print('Migration tool ready')"
    else
        docker-compose exec app python -c "from app.infrastructure.database.tools import migration_tool; print('Migration tool ready')"
    fi
    
    print_message "Migrations completed successfully."
}

# 函数：备份数据
backup_data() {
    print_message "Creating data backup..."
    
    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份PostgreSQL
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres biohub_prod > "$BACKUP_DIR/postgres_backup.sql"
        docker-compose -f docker-compose.prod.yml exec mysql mysqldump -u root -p biohub_prod > "$BACKUP_DIR/mysql_backup.sql"
    else
        docker-compose exec postgres pg_dump -U postgres biohub_dev > "$BACKUP_DIR/postgres_backup.sql"
        docker-compose exec mysql mysqldump -u root -p biohub_dev > "$BACKUP_DIR/mysql_backup.sql"
    fi
    
    print_message "Data backup created at $BACKUP_DIR"
}

# 函数：清理资源
cleanup() {
    print_message "Cleaning up Docker resources..."
    
    # 停止所有容器
    stop_services $1
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    print_message "Cleanup completed."
}

# 函数：健康检查
health_check() {
    print_message "Performing health check..."
    
    # 检查应用健康状态
    if curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_message "✓ Application is healthy"
    else
        print_error "✗ Application is not responding"
    fi
    
    # 检查数据库连接
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml exec app python -c "from app.infrastructure.database.session import health_check; result = health_check(); print('Database health:', result)"
    else
        docker-compose exec app python -c "from app.infrastructure.database.session import health_check; result = health_check(); print('Database health:', result)"
    fi
}

# 主函数
main() {
    case $1 in
        "build")
            check_docker
            build_images $2
            ;;
        "start")
            check_docker
            check_env_files
            start_services $2
            ;;
        "stop")
            stop_services $2
            ;;
        "restart")
            stop_services $2
            start_services $2
            ;;
        "status")
            check_status $2
            ;;
        "logs")
            view_logs $2 $3
            ;;
        "init-db")
            init_database $2
            ;;
        "migrate")
            run_migrations $2
            ;;
        "backup")
            backup_data $2
            ;;
        "cleanup")
            cleanup $2
            ;;
        "health")
            health_check $2
            ;;
        "deploy")
            print_message "Starting full deployment..."
            check_docker
            check_env_files
            build_images $2
            start_services $2
            sleep 60  # 等待服务启动
            init_database $2
            health_check $2
            print_message "Deployment completed successfully!"
            ;;
        *)
            echo "Usage: $0 {build|start|stop|restart|status|logs|init-db|migrate|backup|cleanup|health|deploy} [prod]"
            echo ""
            echo "Commands:"
            echo "  build     - Build Docker images"
            echo "  start     - Start all services"
            echo "  stop      - Stop all services"
            echo "  restart   - Restart all services"
            echo "  status    - Show service status"
            echo "  logs      - View service logs"
            echo "  init-db   - Initialize database"
            echo "  migrate   - Run database migrations"
            echo "  backup    - Create data backup"
            echo "  cleanup   - Clean up Docker resources"
            echo "  health    - Perform health check"
            echo "  deploy    - Full deployment (build + start + init)"
            echo ""
            echo "Options:"
            echo "  prod      - Use production configuration"
            echo ""
            echo "Examples:"
            echo "  $0 deploy          # Deploy in development mode"
            echo "  $0 deploy prod     # Deploy in production mode"
            echo "  $0 logs app        # View application logs"
            echo "  $0 logs app prod   # View production application logs"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"