from typing import Any, List, Optional, TypeVar

from fastapi import status
from fastapi.responses import JSONResponse

from app.shared.utils.json_utils import prepare_response_data
from app.core.logging.log_config import get_logger
from app.shared.schemas.response import (
    ErrorResponse,
    PaginatedResponse,
    SuccessResponse,
)

DataT = TypeVar("DataT")
logger = get_logger("app.response_utils")


def success_response(
    data: Any = None,
    msg: str = "操作成功",
    code: int = 1,
    status_code: int = status.HTTP_200_OK,
) -> JSONResponse:
    """创建成功响应"""
    try:
        # 预处理数据，确保可以JSON序列化
        serializable_data = prepare_response_data(data)
        response = SuccessResponse(data=serializable_data, msg=msg, code=code)

        response_dict = response.dict()

        return JSONResponse(
            status_code=status_code,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as e:
        logger.error(f"创建成功响应失败: {str(e)}")
        # 降级处理：返回简化的响应
        fallback_response = SuccessResponse(
            data={"error": "响应数据序列化失败", "original_type": type(data).__name__},
            msg=msg,
            code=code,
        )
        return JSONResponse(status_code=status_code, content=fallback_response.dict())


def error_response(
    msg: str,
    code: int = 0,
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
) -> JSONResponse:
    """创建错误响应"""
    response = ErrorResponse(msg=msg, code=code)
    return JSONResponse(status_code=status_code, content=response.dict())


def paginated_response(
    items: List[Any],
    total: int,
    page: int,
    page_size: int,
    msg: str = "获取成功",
    code: int = 1,
    status_code: int = status.HTTP_200_OK,
) -> JSONResponse:
    """创建分页响应"""
    try:
        # 预处理列表中的每个项目
        serializable_items = [prepare_response_data(item) for item in items]

        response = PaginatedResponse(
            items=serializable_items,
            total=total,
            page=page,
            page_size=page_size,
            msg=msg,
            code=code,
        )

        response_dict = response.dict()
        return JSONResponse(
            status_code=status_code,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as e:
        logger.error(f"创建分页响应失败: {str(e)}")
        # 降级处理：返回错误信息
        fallback_response = ErrorResponse(
            msg="分页数据序列化失败",
            code=500,
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=fallback_response.dict(),
        )


def created_response(data: Any = None, msg: str = "创建成功") -> JSONResponse:
    """创建资源成功响应"""
    return success_response(
        data=data,
        msg=msg,
        code=1,
        status_code=status.HTTP_201_CREATED,
    )


def no_content_response(msg: str = "操作成功") -> JSONResponse:
    """无内容响应"""
    return success_response(
        data=None,
        msg=msg,
        code=1,
        status_code=status.HTTP_204_NO_CONTENT,
    )


def not_found_response(msg: str = "资源不存在") -> JSONResponse:
    """资源未找到响应"""
    return error_response(
        msg=msg,
        code=404,
        status_code=status.HTTP_404_NOT_FOUND,
    )


def forbidden_response(msg: str = "权限不足") -> JSONResponse:
    """权限不足响应"""
    return error_response(
        msg=msg,
        code=403,
        status_code=status.HTTP_403_FORBIDDEN,
    )


def unauthorized_response(msg: str = "认证失败") -> JSONResponse:
    """认证失败响应"""
    return error_response(
        msg=msg,
        code=401,
        status_code=status.HTTP_401_UNAUTHORIZED,
    )


def bad_request_response(msg: str = "请求参数错误") -> JSONResponse:
    """请求参数错误响应"""
    return error_response(
        msg=msg,
        code=400,
        status_code=status.HTTP_400_BAD_REQUEST,
    )
