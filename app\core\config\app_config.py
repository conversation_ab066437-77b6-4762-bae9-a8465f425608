from typing import Dict, Any
from pydantic import field_validator

from app.core.config.base import BaseConfig
from app.core.config.enums import Environment


class AppConfig(BaseConfig):
    """应用配置"""
    
    # 环境配置
    ENVIRONMENT: Environment = Environment.DEVELOPMENT
    DEBUG: bool = False
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 1
    
    # API配置
    API_V1_STR: str = "/api/v1"
    CORS_ORIGINS: list[str] = []
    
    # 应用信息
    APP_NAME: str = "Biohub AI"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "A agent for BIO"
    
    @field_validator("ENVIRONMENT")
    def validate_environment(cls, v):
        if v not in Environment:
            raise ValueError(f"Invalid environment: {v}")
        return v
    
    @field_validator("PORT")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
    
    @field_validator("WORKERS")
    def validate_workers(cls, v):
        if v < 1:
            raise ValueError("Workers must be at least 1")
        return v
    
    def validate_config(self) -> None:
        """验证配置"""
        if self.ENVIRONMENT == Environment.PRODUCTION and self.DEBUG:
            raise ValueError("Debug mode should not be enabled in production")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "environment": self.ENVIRONMENT,
            "debug": self.DEBUG,
            "host": self.HOST,
            "port": self.PORT,
            "workers": self.WORKERS,
            "api_v1_str": self.API_V1_STR,
            "app_name": self.APP_NAME,
            "app_version": self.APP_VERSION,
        }