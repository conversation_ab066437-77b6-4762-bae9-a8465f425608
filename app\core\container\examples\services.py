from typing import Optional
from sqlalchemy.orm import Session

from app.core.container.decorators import injectable, singleton, depends
from app.core.container.providers.cache import CacheService


@singleton("user_service")
@injectable
class UserService:
    """用户服务示例"""
    
    def __init__(self, cache_service: CacheService, logger_factory):
        self.cache_service = cache_service
        self.logger = logger_factory("user_service")
    
    def get_user(self, user_id: str) -> Optional[dict]:
        """获取用户信息"""
        # 先从缓存获取
        cache_key = f"user:{user_id}"
        cached_user = self.cache_service.get(cache_key)
        
        if cached_user:
            self.logger.info(f"获取用户 {user_id} 从缓存")
            return {"id": user_id, "name": cached_user}
        
        # 从数据库获取（示例）
        user = {"id": user_id, "name": f"User {user_id}"}
        
        # 存入缓存
        self.cache_service.set(cache_key, user["name"], expire=300)
        
        self.logger.info(f"获取用户 {user_id} 从数据库")
        return user
    
    def create_user(self, user_data: dict) -> dict:
        """创建用户"""
        # 创建用户逻辑
        user_id = user_data.get("id")
        self.logger.info(f"创建用户 {user_id}")
        
        # 清除相关缓存
        cache_key = f"user:{user_id}"
        self.cache_service.delete(cache_key)
        
        return user_data


@singleton("knowledge_base_service")
@injectable
class KnowledgeBaseService:
    """知识库服务示例"""
    
    def __init__(self, user_service: UserService, logger_factory):
        self.user_service = user_service
        self.logger = logger_factory("knowledge_base_service")
    
    def create_knowledge_base(self, kb_data: dict, user_id: str) -> dict:
        """创建知识库"""
        # 验证用户
        user = self.user_service.get_user(user_id)
        if not user:
            raise ValueError(f"用户 {user_id} 不存在")
        
        # 创建知识库
        kb_id = kb_data.get("id")
        self.logger.info(f"用户 {user_id} 创建知识库 {kb_id}")
        
        return {
            "id": kb_id,
            "name": kb_data.get("name"),
            "owner": user,
            "created_at": "2023-01-01T00:00:00Z"
        }