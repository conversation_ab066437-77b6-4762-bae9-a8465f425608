from typing import Any, Dict, List, Optional, Union
from functools import wraps
from contextlib import contextmanager

from app.core.exceptions.base import BaseException, ErrorContext
from app.core.exceptions.business import (
    ValidationException,
    BusinessException,
    DatabaseException,
    ExternalServiceException,
    ResourceNotFoundException,
    AuthenticationException,
    AuthorizationException,
)


def handle_exceptions(
    *exception_types: type,
    reraise: bool = False,
    default_message: str = "操作失败",
    should_log: bool = True
):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except exception_types as e:
                if reraise:
                    raise
                
                # 如果是已知的业务异常，直接抛出
                if isinstance(e, BaseException):
                    raise e
                
                # 转换为业务异常
                raise BusinessException(
                    message=f"{default_message}: {str(e)}",
                    should_log=should_log,
                ) from e
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exception_types as e:
                if reraise:
                    raise
                
                if isinstance(e, BaseException):
                    raise e
                
                raise BusinessException(
                    message=f"{default_message}: {str(e)}",
                    should_log=should_log,
                ) from e
        
        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def validate_required(data: Dict[str, Any], required_fields: List[str]):
    """验证必填字段"""
    errors = {}
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == "":
            errors[field] = f"{field} 是必填字段"
    
    if errors:
        raise ValidationException(
            message="必填字段验证失败",
            field_errors=errors
        )


def validate_field_length(data: Dict[str, Any], field_limits: Dict[str, Dict[str, int]]):
    """验证字段长度"""
    errors = {}
    for field, limits in field_limits.items():
        if field in data and data[field] is not None:
            value = str(data[field])
            min_length = limits.get("min", 0)
            max_length = limits.get("max", float('inf'))
            
            if len(value) < min_length:
                errors[field] = f"{field} 长度不能少于 {min_length} 个字符"
            elif len(value) > max_length:
                errors[field] = f"{field} 长度不能超过 {max_length} 个字符"
    
    if errors:
        raise ValidationException(
            message="字段长度验证失败",
            field_errors=errors
        )


def validate_email(email: str, field_name: str = "email"):
    """验证邮箱格式"""
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not re.match(email_pattern, email):
        raise ValidationException(
            message="邮箱格式验证失败",
            field_errors={field_name: "邮箱格式不正确"}
        )


def validate_phone(phone: str, field_name: str = "phone"):
    """验证手机号格式"""
    import re
    phone_pattern = r'^1[3-9]\d{9}$'
    
    if not re.match(phone_pattern, phone):
        raise ValidationException(
            message="手机号格式验证失败",
            field_errors={field_name: "手机号格式不正确"}
        )


def check_permission(user_id: str, required_permissions: List[str]):
    """检查用户权限"""
    # 这里应该实现真实的权限检查逻辑
    # 示例实现
    user_permissions = []  # 从数据库或缓存获取用户权限
    
    missing_permissions = [p for p in required_permissions if p not in user_permissions]
    
    if missing_permissions:
        raise AuthorizationException(
            message=f"缺少权限: {', '.join(missing_permissions)}"
        )


def check_resource_exists(resource_id: Any, resource_type: str, get_resource_func: callable):
    """检查资源是否存在"""
    try:
        resource = get_resource_func(resource_id)
        if resource is None:
            raise ResourceNotFoundException(
                message=f"{resource_type} {resource_id} 不存在",
                resource_type=resource_type
            )
        return resource
    except Exception as e:
        if isinstance(e, BaseException):
            raise
        raise DatabaseException(
            message=f"查询{resource_type}失败: {str(e)}"
        ) from e


@contextmanager
def transaction_context(db_session):
    """数据库事务上下文管理器"""
    try:
        yield db_session
        db_session.commit()
    except Exception as e:
        db_session.rollback()
        if isinstance(e, BaseException):
            raise
        raise DatabaseException(
            message=f"数据库事务执行失败: {str(e)}"
        ) from e
    finally:
        db_session.close()


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            import asyncio
            
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except (ExternalServiceException, DatabaseException) as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(delay * (backoff ** attempt))
                    else:
                        raise
                except Exception as e:
                    # 对于其他类型的异常，不重试
                    raise
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            import time
            
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (ExternalServiceException, DatabaseException) as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (backoff ** attempt))
                    else:
                        raise
                except Exception as e:
                    raise
            
            raise last_exception
        
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ExceptionCollector:
    """异常收集器 - 用于批量处理异常"""
    
    def __init__(self):
        self.exceptions: List[BaseException] = []
    
    def add_exception(self, exception: BaseException):
        """添加异常"""
        self.exceptions.append(exception)
    
    def add_validation_error(self, field: str, message: str):
        """添加验证错误"""
        validation_error = ValidationException(
            message=f"字段验证失败: {field}",
            field_errors={field: message}
        )
        self.add_exception(validation_error)
    
    def has_exceptions(self) -> bool:
        """是否有异常"""
        return len(self.exceptions) > 0
    
    def get_first_exception(self) -> Optional[BaseException]:
        """获取第一个异常"""
        return self.exceptions[0] if self.exceptions else None
    
    def raise_if_any(self):
        """如果有异常则抛出第一个"""
        if self.has_exceptions():
            raise self.exceptions[0]
    
    def clear(self):
        """清空异常"""
        self.exceptions.clear()