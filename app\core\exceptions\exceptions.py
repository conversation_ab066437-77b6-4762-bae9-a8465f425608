from typing import Any, Dict, Optional

from fastapi import HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError


from app.core.logging.log_config import get_logger
from app.shared.schemas.response import ErrorResponse

logger = get_logger("app.exceptions")


class BioHubException(Exception):
    """自定义基础异常类"""

    def __init__(
        self,
        msg: str,
        code: Optional[int] = None,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.msg = msg
        self.code = code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.msg)


class ValidationException(BioHubException):
    """数据验证异常"""

    def __init__(self, msg: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            msg=msg,
            code=422,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


class AuthenticationException(BioHubException):
    """认证异常"""

    def __init__(self, msg: str = "认证失败"):
        super().__init__(
            msg=msg,
            code=401,
            status_code=status.HTTP_401_UNAUTHORIZED,
        )


class AuthorizationException(BioHubException):
    """授权异常"""

    def __init__(self, msg: str = "权限不足"):
        super().__init__(
            msg=msg,
            code=403,
            status_code=status.HTTP_403_FORBIDDEN,
        )


class ResourceNotFoundException(BioHubException):
    """资源未找到异常"""

    def __init__(self, msg: str = "资源不存在"):
        super().__init__(
            msg=msg,
            code=404,
            status_code=status.HTTP_404_NOT_FOUND,
        )


class DatabaseException(BioHubException):
    """数据库异常"""

    def __init__(self, msg: str = "数据库操作失败"):
        super().__init__(
            msg=msg,
            code=50002,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class BusinessException(BioHubException):
    """业务逻辑异常"""

    def __init__(self, msg: str = "业务逻辑异常"):
        super().__init__(
            msg=msg,
            code=50003,
            status_code=status.HTTP_400_BAD_REQUEST,
        )


async def biohub_exception_handler(
    request: Request, exc: BioHubException
) -> JSONResponse:
    """自定义异常处理器"""
    request_id = getattr(request.state, "request_id", "unknown")

    logger.error(
        f"BioHub异常: {exc.msg}",
        extra={
            "request_id": request_id,
            "code": exc.code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method,
        },
    )

    response = ErrorResponse(msg=exc.msg, code=exc.code)

    try:
        response_dict = response.dict()
        return JSONResponse(
            status_code=exc.status_code,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as json_error:
        logger.error(f"异常响应序列化失败: {str(json_error)}")
        # 降级处理：返回最基本的错误响应
        basic_response = {
            "success": False,
            "data": None,
            "msg": "服务器内部错误",
            "code": "50001",
        }
        return JSONResponse(status_code=exc.status_code, content=basic_response)


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    request_id = getattr(request.state, "request_id", "unknown")

    logger.error(
        f"HTTP异常: {exc.detail}",
        extra={
            "request_id": request_id,
            "code": None,
            "status_code": exc.status_code,
            "path": request.url.path,
            "method": request.method,
        },
    )

    response = ErrorResponse(message=str(exc.detail), code=None)

    try:
        response_dict = response.dict()
        return JSONResponse(
            status_code=exc.status_code,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as json_error:
        logger.error(f"HTTP异常响应序列化失败: {str(json_error)}")
        basic_response = {
            "success": False,
            "data": None,
            "msg": str(exc.detail),
            "code": None,
        }
        return JSONResponse(status_code=exc.status_code, content=basic_response)


async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """请求验证异常处理器"""
    request_id = getattr(request.state, "request_id", "unknown")
    # 格式化验证错误信息
    errors = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        errors.append(f"{field}: {message}")

    error_message = "请求参数验证失败: " + "; ".join(errors)

    logger.warning(
        f"请求验证失败: {error_message}",
        extra={
            "request_id": request_id,
            "code": 422,
            "validation_errors": exc.errors(),
            "path": request.url.path,
            "method": request.method,
        },
    )

    response = ErrorResponse(msg=error_message, code=422)

    try:
        response_dict = response.dict()
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as json_error:
        logger.error(f"验证异常响应序列化失败: {str(json_error)}")
        basic_response = {
            "success": False,
            "data": None,
            "msg": "请求参数验证失败",
            "code": 422,
        }
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, content=basic_response
        )


async def sqlalchemy_exception_handler(
    request: Request, exc: SQLAlchemyError
) -> JSONResponse:
    """SQLAlchemy异常处理器"""
    request_id = getattr(request.state, "request_id", "unknown")

    logger.error(
        f"数据库异常: {str(exc)}",
        extra={
            "request_id": request_id,
            "code": 50002,
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__,
        },
    )

    response = ErrorResponse(msg="数据库操作失败", code=50002)

    try:
        response_dict = response.dict()
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as json_error:
        logger.error(f"数据库异常响应序列化失败: {str(json_error)}")
        basic_response = {
            "success": False,
            "data": None,
            "msg": "数据库操作失败",
            "code": 50002,
        }
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=basic_response
        )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器 - 捕获所有未被处理的异常"""
    request_id = getattr(request.state, "request_id", "unknown")

    logger.error(
        f"未处理的异常: {str(exc)}",
        extra={
            "request_id": request_id,
            "code": 50001,
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__,
        },
        exc_info=True,  # 记录完整的异常堆栈
    )

    response = ErrorResponse(msg="服务器内部错误", code=50001)

    try:
        response_dict = response.dict()
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_dict,
            media_type="application/json",
        )
    except Exception as json_error:
        logger.error(f"通用异常响应序列化失败: {str(json_error)}")
        # 最后的降级处理：返回最基本的响应
        basic_response = {
            "success": False,
            "data": None,
            "msg": "服务器内部错误",
            "code": 50001,
        }
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=basic_response
        )
