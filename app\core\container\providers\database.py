from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.container.container import Container, ServiceProvider
from app.core.config.settings import settings


class DatabaseServiceProvider(ServiceProvider):
    """数据库服务提供者"""
    
    def register(self, container: Container) -> None:
        """注册数据库服务"""
        
        # PostgreSQL 引擎
        def create_pg_engine():
            return create_engine(
                settings.database.postgres_url,
                pool_size=settings.database.DB_POOL_SIZE,
                max_overflow=settings.database.DB_MAX_OVERFLOW,
                pool_timeout=settings.database.DB_POOL_TIMEOUT,
                pool_recycle=settings.database.DB_POOL_RECYCLE,
                echo=settings.app.DEBUG,
            )
        
        # MySQL 引擎
        def create_mysql_engine():
            return create_engine(
                settings.database.mysql_url,
                pool_size=settings.database.DB_POOL_SIZE,
                max_overflow=settings.database.DB_MAX_OVERFLOW,
                pool_timeout=settings.database.DB_POOL_TIMEOUT,
                pool_recycle=settings.database.DB_POOL_RECYCLE,
                echo=settings.app.DEBUG,
            )
        
        # PostgreSQL 会话工厂
        def create_pg_session_factory():
            engine = container.get("pg_engine")
            return sessionmaker(bind=engine)
        
        # MySQL 会话工厂
        def create_mysql_session_factory():
            engine = container.get("mysql_engine")
            return sessionmaker(bind=engine)
        
        # 注册服务
        container.singleton("pg_engine", create_pg_engine)
        container.singleton("mysql_engine", create_mysql_engine)
        container.singleton("pg_session_factory", create_pg_session_factory)
        container.singleton("mysql_session_factory", create_mysql_session_factory)