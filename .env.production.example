# 生产环境配置示例
# 复制此文件并重命名为 .env.production 来使用

# 环境配置
APP__ENVIRONMENT=production
APP__DEBUG=false
APP__HOST=0.0.0.0
APP__PORT=8000
APP__WORKERS=4

# 数据库配置
DATABASE__POSTGRES_SERVER=your-postgres-server
DATABASE__POSTGRES_USER=your-postgres-user
DATABASE__POSTGRES_PASSWORD=your-postgres-password
DATABASE__POSTGRES_DB=biohub_prod
DATABASE__POSTGRES_PORT=5432

DATABASE__MYSQL_SERVER=your-mysql-server
DATABASE__MYSQL_USER=your-mysql-user
DATABASE__MYSQL_PASSWORD=your-mysql-password
DATABASE__MYSQL_DB=biohub_prod
DATABASE__MYSQL_PORT=3306

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# Elasticsearch配置
ELASTICSEARCH_URL=your-elasticsearch-url
ELASTICSEARCH_API_KEY=your-elasticsearch-api-key
ELASTICSEARCH_INDEX_PREFIX=biohub_prod_

# AI服务配置
DASHSCOPE_API_KEY=your-production-dashscope-api-key

# 安全配置
SECURITY__AUTH_SERVICE_URL=https://your-auth-service/api
SECURITY__JWT_SECRET_KEY=your-production-jwt-secret-key-minimum-32-characters
SECURITY__JWT_ALGORITHM=HS256
SECURITY__JWT_EXPIRE_MINUTES=60
SECURITY__RATE_LIMIT_ENABLED=true
SECURITY__RATE_LIMIT_REQUESTS=100
SECURITY__RATE_LIMIT_WINDOW=60

# SSL配置
SECURITY__SSL_ENABLED=true
SECURITY__SSL_CERT_PATH=/path/to/ssl/cert.pem
SECURITY__SSL_KEY_PATH=/path/to/ssl/key.pem

# 日志配置
LOGGING__LOG_LEVEL=INFO
LOGGING__LOG_FILE=logs/app.log
LOGGING__CONSOLE_LOG_ENABLED=false
LOGGING__STRUCTURED_LOGGING=true
LOGGING__JSON_LOGS=true

# 存储配置
UPLOAD_DIR=/var/uploads
MAX_UPLOAD_SIZE=104857600  # 100MB