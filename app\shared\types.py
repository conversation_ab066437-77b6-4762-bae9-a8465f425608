"""
类型定义文件
定义项目中使用的通用类型和接口
"""

from typing import (
    Any, Dict, List, Optional, Union, Callable, TypeVar, Generic, Protocol, 
    Tuple, Set, FrozenSet, Mapping, Sequence, Iterable, Iterator, AsyncIterator,
    Awaitable, Coroutine, Type, ClassVar, Final, Literal, TypedDict, NewType
)
from datetime import datetime, date, time, timedelta
from decimal import Decimal
from enum import Enum
from uuid import UUID
from pathlib import Path
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 基础类型别名
UserId = NewType('UserId', str)
"""用户ID类型"""

KnowledgeBaseId = NewType('KnowledgeBaseId', int)
"""知识库ID类型"""

DocumentId = NewType('DocumentId', int)
"""文档ID类型"""

RequestId = NewType('RequestId', str)
"""请求ID类型"""

SessionId = NewType('SessionId', str)
"""会话ID类型"""

# 通用类型
T = TypeVar('T')
"""通用类型变量"""

K = TypeVar('K')
"""键类型变量"""

V = TypeVar('V')
"""值类型变量"""

# 数据类型
JsonDict = Dict[str, Any]
"""JSON字典类型"""

JsonList = List[Any]
"""JSON列表类型"""

JsonValue = Union[None, bool, int, float, str, JsonDict, JsonList]
"""JSON值类型"""

# 时间类型
Timestamp = Union[datetime, int, float]
"""时间戳类型"""

DateRange = Tuple[date, date]
"""日期范围类型"""

TimeRange = Tuple[time, time]
"""时间范围类型"""

# 分页类型
@dataclass
class PaginationParams:
    """分页参数"""
    page: int = 1
    per_page: int = 20
    skip: int = 0
    limit: int = 20

@dataclass
class PaginationResult(Generic[T]):
    """分页结果"""
    items: List[T]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool

# 过滤类型
FilterValue = Union[str, int, float, bool, List[Any], Dict[str, Any]]
"""过滤值类型"""

FilterDict = Dict[str, FilterValue]
"""过滤字典类型"""

# 排序类型
SortDirection = Literal['asc', 'desc']
"""排序方向类型"""

SortField = Tuple[str, SortDirection]
"""排序字段类型"""

SortOrder = List[SortField]
"""排序顺序类型"""

# 响应类型
class APIResponse(TypedDict):
    """API响应类型"""
    success: bool
    message: str
    data: Optional[Any]
    errors: Optional[List[str]]
    metadata: Optional[Dict[str, Any]]

class ErrorResponse(TypedDict):
    """错误响应类型"""
    error_id: str
    code: str
    message: str
    details: List[Dict[str, Any]]
    severity: str
    timestamp: Optional[str]
    request_id: Optional[str]

# 配置类型
class DatabaseConfig(TypedDict):
    """数据库配置类型"""
    host: str
    port: int
    user: str
    password: str
    database: str
    pool_size: int
    max_overflow: int
    pool_timeout: int
    pool_recycle: int

class RedisConfig(TypedDict):
    """Redis配置类型"""
    host: str
    port: int
    db: int
    password: Optional[str]
    url: Optional[str]

# 业务类型
class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class KnowledgeBasePermission(Enum):
    """知识库权限枚举"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"

class DocumentStatus(Enum):
    """文档状态枚举"""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

# 文件类型
FileSize = NewType('FileSize', int)
"""文件大小类型（字节）"""

MimeType = NewType('MimeType', str)
"""MIME类型"""

class FileInfo(TypedDict):
    """文件信息类型"""
    name: str
    size: FileSize
    mime_type: MimeType
    path: str
    upload_time: datetime

# 协议类型
class Repository(Protocol[T]):
    """仓储协议"""
    
    def get(self, id: Any) -> Optional[T]:
        """获取单个对象"""
        ...
    
    def get_multi(self, skip: int = 0, limit: int = 100) -> List[T]:
        """获取多个对象"""
        ...
    
    def create(self, obj_in: Any) -> T:
        """创建对象"""
        ...
    
    def update(self, id: Any, obj_in: Any) -> Optional[T]:
        """更新对象"""
        ...
    
    def delete(self, id: Any) -> bool:
        """删除对象"""
        ...

class Service(Protocol[T]):
    """服务协议"""
    
    def get_by_id(self, id: Any) -> Optional[T]:
        """根据ID获取"""
        ...
    
    def create(self, data: Any) -> T:
        """创建"""
        ...
    
    def update(self, id: Any, data: Any) -> Optional[T]:
        """更新"""
        ...
    
    def delete(self, id: Any) -> bool:
        """删除"""
        ...

class Cache(Protocol):
    """缓存协议"""
    
    def get(self, key: str) -> Optional[str]:
        """获取缓存"""
        ...
    
    def set(self, key: str, value: str, expire: Optional[int] = None) -> bool:
        """设置缓存"""
        ...
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        ...

class Logger(Protocol):
    """日志协议"""
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """调试日志"""
        ...
    
    def info(self, message: str, *args, **kwargs) -> None:
        """信息日志"""
        ...
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """警告日志"""
        ...
    
    def error(self, message: str, *args, **kwargs) -> None:
        """错误日志"""
        ...

# 函数类型
EventHandler = Callable[[Any], None]
"""事件处理器类型"""

AsyncEventHandler = Callable[[Any], Awaitable[None]]
"""异步事件处理器类型"""

Validator = Callable[[Any], bool]
"""验证器类型"""

AsyncValidator = Callable[[Any], Awaitable[bool]]
"""异步验证器类型"""

Serializer = Callable[[Any], str]
"""序列化器类型"""

Deserializer = Callable[[str], Any]
"""反序列化器类型"""

# 中间件类型
MiddlewareFunction = Callable[[Any], Any]
"""中间件函数类型"""

AsyncMiddlewareFunction = Callable[[Any], Awaitable[Any]]
"""异步中间件函数类型"""

# 上下文类型
class RequestContext(TypedDict):
    """请求上下文类型"""
    request_id: RequestId
    user_id: Optional[UserId]
    session_id: Optional[SessionId]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime

class ErrorContext(TypedDict):
    """错误上下文类型"""
    error_id: str
    request_id: RequestId
    user_id: Optional[UserId]
    path: Optional[str]
    method: Optional[str]
    timestamp: datetime
    additional_data: Optional[Dict[str, Any]]

# 事件类型
class Event(TypedDict):
    """事件类型"""
    type: str
    data: Any
    timestamp: datetime
    source: str

class DomainEvent(Event):
    """领域事件类型"""
    aggregate_id: str
    version: int

# 查询类型
class Query(TypedDict):
    """查询类型"""
    filters: Optional[FilterDict]
    sort: Optional[SortOrder]
    pagination: Optional[PaginationParams]

class SearchQuery(Query):
    """搜索查询类型"""
    keyword: str
    fields: Optional[List[str]]
    highlight: bool

# 导出所有类型
__all__ = [
    # 基础类型
    'UserId', 'KnowledgeBaseId', 'DocumentId', 'RequestId', 'SessionId',
    'T', 'K', 'V',
    'JsonDict', 'JsonList', 'JsonValue',
    'Timestamp', 'DateRange', 'TimeRange',
    'FilterValue', 'FilterDict',
    'SortDirection', 'SortField', 'SortOrder',
    'FileSize', 'MimeType',
    
    # 数据类
    'PaginationParams', 'PaginationResult',
    'FileInfo',
    
    # 类型字典
    'APIResponse', 'ErrorResponse',
    'DatabaseConfig', 'RedisConfig',
    'RequestContext', 'ErrorContext',
    'Event', 'DomainEvent',
    'Query', 'SearchQuery',
    
    # 枚举
    'UserRole', 'KnowledgeBasePermission', 'DocumentStatus',
    
    # 协议
    'Repository', 'Service', 'Cache', 'Logger',
    
    # 函数类型
    'EventHandler', 'AsyncEventHandler',
    'Validator', 'AsyncValidator',
    'Serializer', 'Deserializer',
    'MiddlewareFunction', 'AsyncMiddlewareFunction',
]