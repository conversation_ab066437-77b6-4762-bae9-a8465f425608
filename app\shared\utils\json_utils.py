import datetime
import decimal
import json
from enum import Enum
from typing import Any, Dict, List, Union
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.ext.declarative import DeclarativeMeta
from sqlalchemy.inspection import inspect
from sqlalchemy.orm import class_mapper

from app.core.logging.log_config import get_logger

logger = get_logger("app.json_utils")


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理特殊类型的序列化"""

    def default(self, obj: Any) -> Any:
        """处理默认JSON编码器无法处理的对象"""
        try:
            # 处理datetime对象
            if isinstance(obj, (datetime.datetime, datetime.date)):
                return obj.isoformat()

            # 处理time对象
            if isinstance(obj, datetime.time):
                return obj.isoformat()

            # 处理Decimal对象
            if isinstance(obj, decimal.Decimal):
                return float(obj)

            # 处理UUID对象
            if isinstance(obj, UUID):
                return str(obj)

            # 处理枚举对象
            if isinstance(obj, Enum):
                return obj.value

            # 处理Pydantic模型
            if isinstance(obj, BaseModel):
                return obj.dict()

            # 处理SQLAlchemy模型
            if hasattr(obj, "__table__"):
                return sqlalchemy_to_dict(obj)

            # 处理set对象
            if isinstance(obj, set):
                return list(obj)

            # 处理bytes对象
            if isinstance(obj, bytes):
                try:
                    return obj.decode("utf-8")
                except UnicodeDecodeError:
                    return obj.hex()

            # 如果对象有__dict__属性，尝试序列化其属性
            if hasattr(obj, "__dict__"):
                return {
                    key: value
                    for key, value in obj.__dict__.items()
                    if not key.startswith("_")
                }

            # 如果对象有__str__方法，返回字符串表示
            if hasattr(obj, "__str__"):
                return str(obj)

        except Exception as e:
            logger.warning(f"序列化对象失败: {type(obj).__name__}, 错误: {str(e)}")
            return f"<不可序列化对象: {type(obj).__name__}>"

        # 调用父类的default方法
        return super().default(obj)


def sqlalchemy_to_dict(obj: Any, exclude_private: bool = True) -> Dict[str, Any]:
    """将SQLAlchemy模型对象转换为字典"""
    if not hasattr(obj, "__table__"):
        raise ValueError("对象不是SQLAlchemy模型")

    try:
        # 获取模型的所有列
        mapper = class_mapper(obj.__class__)
        result = {}

        # 处理普通列
        for column in mapper.columns:
            column_name = column.name
            if exclude_private and column_name.startswith("_"):
                continue

            value = getattr(obj, column_name, None)
            result[column_name] = serialize_value(value)

        # 处理关系属性（可选，可能导致循环引用）
        # for relationship in mapper.relationships:
        #     rel_name = relationship.key
        #     if exclude_private and rel_name.startswith('_'):
        #         continue
        #
        #     rel_value = getattr(obj, rel_name, None)
        #     if rel_value is not None:
        #         if hasattr(rel_value, '__iter__') and not isinstance(rel_value, str):
        #             # 一对多关系
        #             result[rel_name] = [sqlalchemy_to_dict(item) for item in rel_value]
        #         else:
        #             # 一对一关系
        #             result[rel_name] = sqlalchemy_to_dict(rel_value)

        return result

    except Exception as e:
        logger.error(f"SQLAlchemy对象序列化失败: {str(e)}")
        # 降级处理：使用对象的__dict__
        return {
            key: serialize_value(value)
            for key, value in obj.__dict__.items()
            if not key.startswith("_") or not exclude_private
        }


def serialize_value(value: Any) -> Any:
    """序列化单个值"""
    if value is None:
        return None

    # 基本类型直接返回
    if isinstance(value, (str, int, float, bool)):
        return value

    # 使用自定义编码器处理复杂类型
    try:
        return json.loads(json.dumps(value, cls=CustomJSONEncoder))
    except Exception:
        return str(value)


def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化，确保不会抛出异常"""
    try:
        return json.dumps(obj, cls=CustomJSONEncoder, ensure_ascii=False, **kwargs)
    except Exception as e:
        logger.error(f"JSON序列化失败: {str(e)}")
        # 降级处理：返回错误信息的JSON
        error_obj = {
            "error": "序列化失败",
            "error_type": type(e).__name__,
            "error_message": str(e),
            "original_type": type(obj).__name__,
        }
        return json.dumps(error_obj, ensure_ascii=False)


def safe_serialize_data(data: Any) -> Any:
    """安全序列化数据，返回可JSON序列化的对象"""
    try:
        # 先尝试直接序列化
        json.dumps(data)
        return data
    except (TypeError, ValueError):
        # 如果失败，使用自定义编码器处理
        try:
            serialized_str = json.dumps(data, cls=CustomJSONEncoder)
            return json.loads(serialized_str)
        except Exception as e:
            logger.warning(f"数据序列化失败: {str(e)}")
            return {
                "error": "数据无法序列化",
                "type": type(data).__name__,
                "message": str(data)
                if hasattr(data, "__str__")
                else "无法转换为字符串",
            }


def prepare_response_data(data: Any) -> Any:
    """准备响应数据，确保可以被JSON序列化"""
    if data is None:
        return None

    # 如果是列表，递归处理每个元素
    if isinstance(data, list):
        return [prepare_response_data(item) for item in data]

    # 如果是字典，递归处理每个值
    if isinstance(data, dict):
        return {key: prepare_response_data(value) for key, value in data.items()}

    # 如果是SQLAlchemy模型
    if hasattr(data, "__table__"):
        return sqlalchemy_to_dict(data)

    # 如果是Pydantic模型
    if isinstance(data, BaseModel):
        return data.dict()

    # 其他情况使用安全序列化
    return safe_serialize_data(data)
