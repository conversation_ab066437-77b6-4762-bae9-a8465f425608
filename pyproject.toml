[project]
name = "ai-biohub"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "agno==1.7.10",
    "aiohappyeyeballs==2.6.1",
    "aiohttp==3.12.2",
    "aiosignal==1.3.2",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "asyncpg==0.30.0",
    "attrs==25.3.0",
    "certifi==2025.4.26",
    "cffi==1.17.1",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "cryptography==45.0.3",
    "distro==1.9.0",
    "dnspython==2.7.0",
    "docstring-parser==0.16",
    "docx==0.2.4",
    "elastic-transport==8.17.1",
    "elasticsearch==8.18.1",
    "elasticsearch-dsl==8.18.0",
    "email-validator==2.2.0",
    "fastapi==0.115.12",
    "fastapi-cli==0.0.7",
    "frozenlist==1.6.0",
    "gitdb==4.0.12",
    "gitpython==3.1.44",
    "greenlet==3.2.2",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "idna==3.10",
    "iniconfig==2.1.0",
    "jinja2==3.1.6",
    "jiter==0.10.0",
    "lxml==5.4.0",
    "markdown-it-py==3.0.0",
    "markupsafe==3.0.2",
    "mdurl==0.1.2",
    "multidict==6.4.4",
    "numpy==2.2.6",
    "openai==1.82.0",
    "packaging==25.0",
    "pgvector==0.4.1",
    "pillow==11.2.1",
    "pluggy==1.6.0",
    "propcache==0.3.1",
    "psycopg==3.2.9",
    "psycopg-binary==3.2.9",
    "psycopg2-binary==2.9.10",
    "pycparser==2.22",
    "pydantic==2.11.5",
    "pydantic-core==2.33.2",
    "pydantic-settings==2.9.1",
    "pygments==2.19.1",
    "pyjwt==2.10.1",
    "pymupdf==1.26.1",
    "pymysql==1.1.1",
    "pytest==8.3.5",
    "pytest-asyncio==1.0.0",
    "python-dateutil==2.9.0.post0",
    "python-docx==1.1.2",
    "python-dotenv==1.1.0",
    "python-multipart==0.0.20",
    "pyyaml==6.0.2",
    "redis==6.2.0",
    "requests==2.32.3",
    "rich==14.0.0",
    "rich-toolkit==0.14.6",
    "setuptools==78.1.1",
    "shellingham==1.5.4",
    "six==1.17.0",
    "smmap==5.0.2",
    "sniffio==1.3.1",
    "sqlalchemy==2.0.41",
    "sqlalchemy-utils==0.41.2",
    "sse-starlette==2.3.6",
    "starlette==0.46.2",
    "tomli==2.2.1",
    "tqdm==4.67.1",
    "typer==0.16.0",
    "typing-extensions==4.13.2",
    "typing-inspection==0.4.1",
    "urllib3==2.4.0",
    "uvicorn==0.34.2",
    "watchfiles==1.0.5",
    "websockets==15.0.1",
    "wheel==0.45.1",
    "yarl==1.20.0",
]

[project.optional-dependencies]

# 可选：为Windows用户提供性能优化的替代方案
windows-performance = []

# Linux 平台可选优化依赖
linux-performance = [
    "uvloop>=0.21.0"
]
