import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.api.versioning import APIVersion


class APIVersionMiddleware(BaseHTTPMiddleware):
    """API版本中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 添加请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-API-Version"] = self._get_api_version_from_path(request.url.path)
        
        return response
    
    def _get_api_version_from_path(self, path: str) -> str:
        """从路径获取API版本"""
        if path.startswith("/api/v1"):
            return APIVersion.V1.value
        elif path.startswith("/api/v2"):
            return APIVersion.V2.value
        return "unknown"


class APIRateLimitMiddleware(BaseHTTPMiddleware):
    """API限流中间件"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.requests = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端IP
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理过期记录
        self._cleanup_expired_records(current_time)
        
        # 检查限制
        if self._is_rate_limited(client_ip, current_time):
            from fastapi import HTTPException
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded",
                headers={"Retry-After": str(self.period)}
            )
        
        # 记录请求
        self._record_request(client_ip, current_time)
        
        # 处理请求
        response = await call_next(request)
        
        # 添加限流头
        remaining = self._get_remaining_requests(client_ip, current_time)
        response.headers["X-RateLimit-Limit"] = str(self.calls)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(current_time + self.period))
        
        return response
    
    def _cleanup_expired_records(self, current_time: float):
        """清理过期记录"""
        expired_ips = []
        for ip, timestamps in self.requests.items():
            self.requests[ip] = [t for t in timestamps if current_time - t < self.period]
            if not self.requests[ip]:
                expired_ips.append(ip)
        
        for ip in expired_ips:
            del self.requests[ip]
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """检查是否被限流"""
        if client_ip not in self.requests:
            return False
        
        recent_requests = [t for t in self.requests[client_ip] if current_time - t < self.period]
        return len(recent_requests) >= self.calls
    
    def _record_request(self, client_ip: str, current_time: float):
        """记录请求"""
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        self.requests[client_ip].append(current_time)
    
    def _get_remaining_requests(self, client_ip: str, current_time: float) -> int:
        """获取剩余请求数"""
        if client_ip not in self.requests:
            return self.calls
        
        recent_requests = [t for t in self.requests[client_ip] if current_time - t < self.period]
        return max(0, self.calls - len(recent_requests))


class APILoggingMiddleware(BaseHTTPMiddleware):
    """API日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取logger
        from app.core.container import container
        logger_factory = container.get("logger_factory")
        logger = logger_factory("api")
        
        # 记录请求
        request_id = getattr(request.state, "request_id", "unknown")
        logger.info(
            f"API Request: {request.method} {request.url.path}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "query_params": str(request.query_params),
                "client_ip": request.client.host,
            }
        )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 记录响应
            logger.info(
                f"API Response: {response.status_code}",
                extra={
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "process_time": response.headers.get("X-Process-Time", "unknown"),
                }
            )
            
            return response
            
        except Exception as e:
            # 记录错误
            logger.error(
                f"API Error: {str(e)}",
                extra={
                    "request_id": request_id,
                    "error": str(e),
                    "exception_type": type(e).__name__,
                },
                exc_info=True
            )
            raise