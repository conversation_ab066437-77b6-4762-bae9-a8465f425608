import logging
import os
from typing import Any, Dict, List

import requests

logger = logging.getLogger(__name__)


def rerank_documents(
    query: str,
    documents: List[str],
    model: str = "gte-rerank-v2",
    top_n: int = 5,
    return_documents: bool = True,
) -> Dict[str, Any]:
    """
    使用百炼API对文档进行重排序

    Args:
        query: 查询文本
        documents: 待排序的文档列表
        model: 使用的模型，默认为"gte-rerank-v2"
        top_n: 返回的排序结果数量，默认为5
        return_documents: 是否在结果中返回文档内容，默认为True

    Returns:
        Dict[str, Any]: 排序结果，包含排序后的文档及其相关性分数

    Raises:
        Exception: 当API请求失败时抛出异常
    """
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        raise ValueError("DASHSCOPE_API_KEY environment variable not set")

    url = (
        "https://dashscope.aliyuncs.com/api/v1/services/rerank/text-rerank/text-rerank"
    )

    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

    payload = {
        "model": model,
        "input": {"query": query, "documents": documents},
        "parameters": {"return_documents": return_documents, "top_n": top_n},
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"Error calling DashScope rerank API: {str(e)}")
        raise
