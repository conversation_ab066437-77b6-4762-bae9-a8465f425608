from datetime import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)

from app.infrastructure.database.session import Base


class KnowledgeBaseMember(Base):
    """知识库成员关联模型"""

    __tablename__ = "knowledge_base_members"

    knowledge_base_id = Column(Integer, primary_key=True)
    user_id = Column(String(50), primary_key=True)
    role = Column(String(50))  # admin, editor, viewer
    created_at = Column(DateTime, default=datetime.utcnow)


class KnowledgeBase(Base):
    """知识库模型"""

    __tablename__ = "knowledge_bases"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True)
    description = Column(Text)
    owner_id = Column(String(50))
    visibility = Column(String(50), default="private")  # 可见范围
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    cmetadata = Column(JSON, default=dict)
    is_deleted = Column(Boolean, default=False)


class Document(Base):
    """文档模型"""

    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    knowledge_base_id = Column(Integer)
    title = Column(String(255), index=True)
    description = Column(Text)
    source = Column(String(50))  # 文档来源：upload, url, scrapy等
    source_type = Column(String(50))  # 文档类型：pdf, docx, txt, markdown等
    file_path = Column(String(255))  # 文件路径，如果是上传的文档
    content_type = Column(String(50))  # 文档类型：pdf, docx, txt, markdown等
    uploaded_by = Column(String(50))  # 上传用户ID
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    cmetadata = Column(JSON, default=dict)
    is_deleted = Column(Boolean, default=False)


class DocumentChunk(Base):
    """文档片段模型"""

    __tablename__ = "document_chunks"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"))
    content = Column(Text)
    chunk_index = Column(Integer)  # 片段在文档中的顺序
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    cmetadata = Column(JSON, default=dict)  # 存储额外信息，如页码、段落编号等
    is_deleted = Column(Boolean, default=False)
