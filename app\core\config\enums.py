from enum import Enum
from typing import Dict, Any

class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

class ConfigCategory(str, Enum):
    """配置类别枚举"""
    APP = "app"
    DATABASE = "database"
    CACHE = "cache"
    SEARCH = "search"
    SECURITY = "security"
    LOGGING = "logging"
    EXTERNAL_SERVICES = "external_services"