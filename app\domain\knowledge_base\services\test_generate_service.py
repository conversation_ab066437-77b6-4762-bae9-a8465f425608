import asyncio
import json
from datetime import datetime
from textwrap import dedent
from typing import Any, AsyncGenerator, Dict, Optional

from agno.agent import Agent
from agno.run.response import RunEvent
from agno.team import Team
from agno.tools import tool
from elasticsearch import Elasticsearch
from elasticsearch_dsl import Q, Search

from app.domain.ai.services.embeddings.dashscope import BailianEmbeddings
from app.domain.ai.services.models.dashscope import BailianLLM
from app.core.config import settings
from app.domain.knowledge_base.services.rerank_service import RerankService


# Agent Prompt 常量配置
class AgentPrompts:
    """Agent 提示词常量配置"""

    # 法规搜索Agent配置
    REGULAR_SEARCHER_DESCRIPTION = "您是一名 GMP（良好生产规范）法规合规助理。您的核心职责是根据用户提供的GMP文件内容，利用工具搜索来精确识别和检索最相关的法规条文。您必须确保所有回答都基于工具搜索获得的结果，并且这些结果与用户文档中描述的具体操作、流程和上下文高度相关且直接适用。"

    REGULAR_SEARCHER_INSTRUCTIONS = dedent(
        """您将严格按照以下步骤完成任务：

1.  **理解用户的GMP文件：**
    * 仔细阅读并全面理解用户提供的GMP文件内容。这可能包括SOP（标准操作规程）、批记录、验证报告、偏差报告或任何其他与GMP相关的文本。
    * 识别文档中描述的所有关键活动、流程、要求和目标。
    * 提取所有关键概念、技术术语、具体程序、职责、以及任何数据或记录保存方面的信息。

2.  **规划工具搜索策略：**
    * 根据步骤1中提取的关键信息，制定详细的搜索查询。这些查询应包含核心术语、短语和概念，以最大化搜索结果的准确性和相关性。
    * 考虑法规知识库的结构和搜索工具的功能，优化查询以获取最佳结果（例如，使用引号进行精确匹配，使用布尔运算符等）。

3.  **使用工具搜索法规条文：**
    * **多次调用您的搜索工具** 执行您在步骤2中制定的查询，或根据初步搜索结果调整并进行新的查询。
    * **目标是尽可能全面地找出所有相关的法规条文。** 如果一次搜索未能覆盖所有方面，或者您认为可以找到更多相关条文，请进行额外的搜索。
    * **必须使用工具** 来获取法规条文信息。

4.  **评估和筛选搜索结果：**
    * 仔细审查**所有工具调用**返回的搜索结果。
    * **严格评估每个结果与用户文档内容的相关性。** 排除任何泛泛的、不相关的或仅仅包含关键词但上下文不符的条文。
    * **只保留**那些与用户文档中描述的具体操作、要求或目的**高度契合、直接相关**的法规条文。
    * 如果工具返回了多条相关条文（可能来自多次搜索），根据其直接性和具体性进行优先级排序，并整合所有相关的发现。

5.  **以JSON数组形式呈现结果：**
    * **只基于工具搜索获得且确认相关的结果进行回答。**
    * **示例输出格式：**
    [
        "为规范药品生产质量管理，根据《中华人民共和国药品管理法》、《中华人民共和国药品管理法实施条例》，制定本规范。",
        "企业高层管理人员应当确保实现既定的质量目标，不同层次的人员以及供应商、经销商应当共同参与并承担各自的责任。",
        "质量控制包括相应的组织机构、文件系统以及取样、检验等，确保物料或产品在放行前完成必要的检验，确认其质量符合要求。",
    ]
6.  **严格遵循"仅基于工具搜索结果回答"原则：**
    * 如果工具搜索未能返回与用户文档内容相关的法规条文，请返回一个空的JSON数组 `[]`。**不允许"凭空捏造"或"猜测"任何法规信息。**
    * 确保所有引用的法规条文都是**准确无误**地来自工具搜索结果。
        """
    )

    # 文档分析Agent配置
    DOCUMENT_READER_DESCRIPTION = "您是一名专业的GMP文档分析专家，擅长从文档中提取关键知识点、识别核心概念，并为培训考题生成提供基础支持。"

    DOCUMENT_READER_INSTRUCTIONS = dedent(
        """
        您将严格按照以下步骤完成任务：
        
        1. **文档内容分析：** 
           * 仔细阅读并全面理解上下文中提供的 `document_content`
           * 识别文档的整体结构、主要章节和核心主题
           * 提取文档中的关键术语、定义和专业概念
        
        2. **知识点提取：**
           * 系统性地提取文档中的所有重要知识点
           * 识别关键流程、操作规范和标准要求
           * 注意文档中强调的重点内容和特殊要求
           * 识别可能需要进一步查询法规的关键主题
        
        3. **知识点分类：**
           * 将提取的知识点按主题或功能进行分类
           * 区分基础概念性知识和操作性知识
           * 标记特别重要或容易混淆的知识点
        
        4. **回答用户问题：**
           * 基于您的分析，准确回答用户关于文档内容的问题
           * 回答必须严格基于文档内容，不要添加外部信息
           * 如遇文档中未涵盖的问题，明确指出信息不足
        
        5. **支持考题生成：**
           * 提供适合转化为考题的关键知识点列表
           * 指出哪些内容适合作为单选题、多选题或判断题
           * 建议可能的考点和测试方向
        
        您的分析将为后续的法规搜索和考题生成提供重要基础，请确保分析全面、准确且有深度。
        """
    )

    # 考题生成Agent配置
    TEST_GENERATOR_DESCRIPTION = "您是一名专业的GMP培训考题设计专家，擅长根据文档内容和相关法规创建高质量的培训测试题目。"

    TEST_GENERATOR_INSTRUCTIONS = dedent(
        """
        您将严格按照以下步骤完成任务：
        
        1. **分析输入资源：**
           * 仔细分析提供的文档内容（document_content）
           * 如果有法规搜索结果，将其与文档内容进行整合分析
           * 识别关键知识点、重要概念和操作规范
        
        2. **设计多样化考题：**
           * 基于分析的知识点，设计不同类型的考题（单选题、多选题、判断题）
           * 确保考题覆盖文档中的核心内容和重要法规要求
           * 设计的题目应有不同难度级别，从基础概念到实际应用
           * 每个题目必须有明确的正确答案，且答案必须基于文档或法规内容
           * **考题数量要求：** 总计生成10-15道题目，其中单选题4-6道，多选题3-5道，判断题3-4道
        
        3. **输出格式要求：**
           * 考题必须以JSON数组格式输出，不需要额外说明或包装
           * 每个考题必须包含问题(question)、答案(answer)、题型(type)和选项(options)
           * 题型必须是以下之一：single(单选题)、multi(多选题)、judge(判断题)
           * 答案必须以选项代号(如["A","B"])的形式给出
        
        4. **考题质量保证：**
           * 确保问题表述清晰、准确，没有歧义
           * 确保选项设计合理，干扰项具有一定迷惑性但不失合理性
           * 确保所有考题都与GMP规范和文档内容直接相关
           * 确保题目之间不重复，覆盖面广泛
        
        输出格式示例：
        [{
            "question":"下列活动中哪些应当有操作规程并有过程与结果记录?（可多选）",
            "answer":["A","B","C"],
            "type":"multi",
            "options":["A: 确认和验证","B: 设备的装配和校准","C: 厂房和设备的维护、清洁和消毒","D: 培训、更衣及卫生等"]
        },
        {
            "question":"成品放行前是否可以发运?",
            "answer":["B"],
            "type":"single",
            "options":["A: 可以","B: 不可以"]
        },
        {
            "question":"自检完成后,是否需要形成自检报告?",
            "answer":["A"],
            "type":"judge",
            "options":["A: 正确","B: 错误"]
        }]
        """
    )

    # Team配置
    TEAM_DESCRIPTION = "您是一个专业的GMP培训考题生成系统，能够协调多个专家Agent共同工作，基于企业培训文档和相关法规，生成高质量的GMP培训考题。"

    TEAM_INSTRUCTIONS = dedent("""
    您将作为团队协调者，按照以下工作流程组织各个专家Agent的工作：
    
    1. **文档分析阶段**
       * 首先调用「文档分析」Agent分析用户提供的文档内容
       * 识别文档中的关键知识点、核心概念和重要流程
       * 提取可能需要进一步查询法规的关键术语和主题
       * 确保全面覆盖文档中的各个章节和主题
    
    2. **法规检索阶段**
       * 基于文档分析结果，调用「相关法规搜索」Agent
       * 搜索与文档内容相关的GMP法规条文
       * 确保搜索多个关键主题，获取全面的法规支持
       * 对搜索结果进行整合，确保法规覆盖面广泛
    
    3. **考题生成阶段**
       * 将文档分析结果和法规搜索结果提供给「培训考题生成」Agent
       * 明确指示生成总计10-15道题目，包括单选题4-6道，多选题3-5道，判断题3-4道
       * 指导考题生成Agent创建多样化、准确的考题
       * 确保考题覆盖文档内容和相关法规要求
       * 要求考题难度分布合理，从基础到应用
    
    4. **质量审核阶段**
       * 审核生成的考题，确保问题清晰、选项合理、答案正确
       * 验证考题是否与文档内容和法规要求一致
       * 确保考题数量符合要求（10-15道题目）
       * 确保考题类型分布合理（单选题4-6道，多选题3-5道，判断题3-4道）
       * 确保考题格式符合要求
    
    5. **结果呈现**
       * 以清晰、专业的方式向用户呈现最终考题
       * 确保输出格式符合JSON数组规范
       * 如果用户有特殊需求（如增加或减少考题数量），根据需求灵活调整
    
    在整个过程中，您需要：
    * 有效协调各个Agent的工作，确保信息顺畅传递
    * 根据用户需求灵活调整工作重点和考题数量
    * 确保最终生成的考题既符合GMP规范要求，又能有效测试学员对文档内容的掌握程度
    * 当用户要求增加考题数量时，可以适当调整考题生成指令，增加总题量和各类型题目数量
    """)

    # JSON提取Agent配置
    JSON_EXTRACT_DESCRIPTION = "你是一名高度专业化的数据提取Agent。你的唯一任务是从提供的原始文本中精准识别并提取考试题目信息，然后将这些信息转换为一个**严格符合指定JSON格式的数组**。你将处理的题目类型包括多选题、单选题和判断题。你的输出必须是**纯粹的JSON字符串**，不包含任何额外的文字、说明或对话。"

    JSON_EXTRACT_INSTRUCTIONS = dedent("""
请严格遵循以下步骤和要求执行任务，确保输出的唯一性：

#### 1. 输入解析

* 分析输入的原始文本，识别其中包含的考试题目。
* 每道题目通常由以下部分组成：
    * **题干**：题目的主要内容。
    * **选项**：A、B、C、D 等可供选择的答案。
    * **答案**：明确指出正确答案的选项字母。

#### 2. 题目类型识别

* 根据以下标准判断每道题目的类型：
    * **`"multi"` (多选题)**：
        * 题干中可能包含"（可多选）"字样。
        * 答案部分包含多个选项字母（例如："A,B,C"）。
    * **`"single"` (单选题)**：
        * 答案部分仅包含一个选项字母。
        * 选项通常不为"正确/错误"类型。
    * **`"judge"` (判断题)**：
        * 选项通常为"A: 正确"、"B: 错误"或类似的二元判断。

#### 3. 数据提取与格式化

* 针对每道题目，精确提取以下数据点并按照JSON结构化：
    * **`"question"`**：提取完整的题干文本，不包含任何选项或答案信息。
    * **`"answer"`**：
        * 对于**多选题**，提取所有正确答案对应的选项字母，形成一个字符串数组（例如 `["A", "B", "C"]`）。
        * 对于**单选题**，提取唯一的正确答案选项字母，形成一个包含单个字符串的数组（例如 `["B"]`）。
        * 对于**判断题**，提取与题目正确判断（通常是"正确"）对应的选项字母，形成一个包含单个字符串的数组（例如 `["A"]`）。
    * **`"type"`**：根据识别结果填入对应的题目类型字符串（`"multi"`、`"single"` 或 `"judge"`）。
    * **`"options"`**：提取题目提供的所有选项。每个选项应包含其前面的字母和完整的选项文本，作为一个独立的字符串存储在数组中（例如 `["A: 选项文本", "B: 另一个选项文本"]`）。

#### 4. 输出要求（最关键）

* 你的最终回答**必须是一个能够直接被解析的JSON字符串**。
* **禁止包含任何额外的文字、寒暄、解释、前导语、结束语或任何非JSON格式的内容。**
* **示例输出格式：**
    [
        {
            "question":"下列活动中哪些应当有操作规程并有过程与结果记录?（可多选）",
            "answer":["A","B","C"],
            "type":"multi",
            "options":["A: 确认和验证","B: 设备的装配和校准","C: 厂房和设备的维护、清洁和消毒","D: 培训、更衣及卫生等"]
        },
        {
            "question":"成品放行前是否可以发运?",
            "answer":["B"],
            "type":"single",
            "options":["A: 可以","B: 不可以"]
        },
        {
            "question":"自检完成后,是否需要形成自检报告?",
            "answer":["A"],
            "type":"judge",
            "options":["A: 正确","B: 错误"]
        }
    ]
    """)


class TestGenerateService:
    """GMP培训考题生成服务"""

    def __init__(self):
        """初始化服务"""
        # 初始化Elasticsearch客户端
        self.client = Elasticsearch(
            hosts=settings.ELASTICSEARCH_URL,
            api_key=settings.ELASTICSEARCH_API_KEY,
        )

        # 初始化嵌入模型
        self.embeddings = BailianEmbeddings(api_key=settings.DASHSCOPE_API_KEY)

        # 初始化LLM模型
        self.llm_model = BailianLLM(
            id="qwen-plus-latest", api_key=settings.DASHSCOPE_API_KEY
        )

    def _create_hybrid_search_tool(
        self, knowledge_base_id: int, document_ids: Optional[list] = None
    ):
        """创建混合搜索工具"""

        @tool(
            name="法规条文搜索",
            description="从知识库中搜索相关的法规条文，结果经过相关性排序，返回前top_n个结果。",
        )
        def hybrid_search(top_n: int, keyword: str):
            """
            混合搜索

            Args:
                top_n (int): 搜索结果数量
                keyword (str): 搜索关键词
            Returns:
                list:  文档内容列表
            """
            query_vector = self.embeddings.get_embedding(keyword)
            s = Search(using=self.client, index="biohub_ai__document_chunks")
            s = s.filter("term", knowledge_base_id=knowledge_base_id)

            if document_ids:
                s = s.filter("terms", document_id=document_ids)

            s = s.query(
                Q(
                    "bool",
                    should=[
                        Q("match", title={"query": keyword}),
                        Q("match", content={"query": keyword, "boost": 0.7}),
                    ],
                    minimum_should_match=1,
                )
            )

            knn_filter_must_clauses = [Q("term", knowledge_base_id=knowledge_base_id)]
            if document_ids:
                knn_filter_must_clauses.append(Q("terms", document_id=document_ids))

            s = s.knn(
                field="embedding",
                k=top_n,
                query_vector=query_vector,
                num_candidates=50,
                boost=0.3,
                filter=Q("bool", must=knn_filter_must_clauses),
            )
            es_search_response = s.execute()
            documents = [hit.content for hit in es_search_response]
            rerank_response = RerankService.rerank(keyword, documents, top_n=top_n)
            result = [result.document.text for result in rerank_response.results]
            return result

        return hybrid_search

    def _create_agents(self, tools):
        """创建Agent实例"""
        # 法规搜索Agent
        regular_searcher = Agent(
            name="相关法规搜索",
            model=self.llm_model,
            description=AgentPrompts.REGULAR_SEARCHER_DESCRIPTION,
            instructions=AgentPrompts.REGULAR_SEARCHER_INSTRUCTIONS,
            tools=tools,
            debug_mode=True,
            show_tool_calls=True,
        )

        # 文档分析Agent
        document_reader = Agent(
            name="文档分析",
            model=self.llm_model,
            description=AgentPrompts.DOCUMENT_READER_DESCRIPTION,
            instructions=AgentPrompts.DOCUMENT_READER_INSTRUCTIONS,
        )

        # 考题生成Agent
        test_generator = Agent(
            name="培训考题生成",
            model=self.llm_model,
            description=AgentPrompts.TEST_GENERATOR_DESCRIPTION,
            instructions=AgentPrompts.TEST_GENERATOR_INSTRUCTIONS,
        )

        return regular_searcher, document_reader, test_generator

    def _create_team(self, regular_searcher, document_reader, test_generator):
        """创建Team实例"""
        team = Team(
            name="GMP培训考题生成系统",
            mode="coordinate",
            model=self.llm_model,
            members=[regular_searcher, document_reader, test_generator],
            description=AgentPrompts.TEAM_DESCRIPTION,
            instructions=AgentPrompts.TEAM_INSTRUCTIONS,
            add_member_tools_to_system_message=False,
            enable_agentic_context=True,
            show_members_responses=True,
            monitoring=True,
        )
        return team

    def _create_json_extract_agent(self):
        """创建JSON提取Agent"""
        json_extract_agent = Agent(
            name="JSON提取",
            model=BailianLLM(
                id="qwen-turbo-latest",
                api_key=settings.DASHSCOPE_API_KEY,
                temperature=0.1,
            ),
            description=AgentPrompts.JSON_EXTRACT_DESCRIPTION,
            instructions=AgentPrompts.JSON_EXTRACT_INSTRUCTIONS,
        )
        return json_extract_agent

    async def generate_test_questions_stream(
        self, task_params: dict
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成考题

        Args:
            document_content: 文档内容

        Yields:
            Dict[str, Any]: SSE事件数据
        """
        try:
            # 发送开始事件
            yield {
                "event": "start",
                "data": {
                    "message": "开始生成考题",
                    "timestamp": datetime.now().isoformat(),
                },
            }
            knowledge_base_id = task_params["knowledge_base_id"]
            document_ids = task_params["document_ids"]
            # 创建Agent和Team
            tools = [self._create_hybrid_search_tool(knowledge_base_id, document_ids)]
            regular_searcher, document_reader, test_generator = self._create_agents(
                tools
            )
            team = self._create_team(regular_searcher, document_reader, test_generator)

            yield {
                "event": "progress",
                "data": {
                    "message": "正在分析文档内容...",
                    "stage": "document_analysis",
                    "timestamp": datetime.now().isoformat(),
                },
            }

            # 运行Team生成考题
            """
            responses = team.run(stream=True, message=task_params["document_content"])
            test_generate_result = ""

            for response in responses:
                if response.event == RunEvent.run_response.value:
                    test_generate_result += response.content
            """
            # 发送进度更新
            yield {
                "event": "progress",
                "data": {
                    "message": "正在生成考题...",
                    "stage": "test_generation",
                    # "content": response.content,
                    "timestamp": datetime.now().isoformat(),
                },
            }

            yield {
                "event": "progress",
                "data": {
                    "message": "正在提取考题JSON...",
                    "stage": "json_extraction",
                    "timestamp": datetime.now().isoformat(),
                },
            }
            """
            # 提取JSON格式的考题
            json_extract_agent = self._create_json_extract_agent()
            test_extract_response = json_extract_agent.run(message=test_generate_result)

            try:
                test_extract_result = json.loads(test_extract_response.content)

                # 发送成功结果
                yield {
                    "event": "success",
                    "data": {
                        "message": "考题生成成功",
                        "questions": test_extract_result,
                        "count": len(test_extract_result),
                        "timestamp": datetime.now().isoformat(),
                    },
                }

            except json.JSONDecodeError as e:
                # JSON解析失败
                yield {
                    "event": "error",
                    "data": {
                        "message": "JSON解析失败",
                        "error": str(e),
                        "raw_content": test_extract_response.content,
                        "timestamp": datetime.now().isoformat(),
                    },
                }
                """

        except Exception as e:
            # 发送错误事件
            yield {
                "event": "error",
                "data": {
                    "message": "考题生成失败",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                },
            }

    async def generate_test_questions(self, document_content: str) -> Dict[str, Any]:
        """同步生成考题

        Args:
            document_content: 文档内容

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            # 创建Agent和Team
            regular_searcher, document_reader, test_generator = self._create_agents()
            team = self._create_team(regular_searcher, document_reader, test_generator)

            # 运行Team生成考题
            responses = team.run(stream=True, message=document_content)
            test_generate_result = ""

            for response in responses:
                if response.event == RunEvent.run_response.value:
                    test_generate_result += response.content

            # 提取JSON格式的考题
            json_extract_agent = self._create_json_extract_agent()
            test_extract_response = json_extract_agent.run(message=test_generate_result)

            try:
                test_extract_result = json.loads(test_extract_response.content)

                return {
                    "success": True,
                    "message": "考题生成成功",
                    "questions": test_extract_result,
                    "count": len(test_extract_result),
                    "timestamp": datetime.now().isoformat(),
                }

            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "message": "JSON解析失败",
                    "error": str(e),
                    "raw_content": test_extract_response.content,
                    "timestamp": datetime.now().isoformat(),
                }

        except Exception as e:
            return {
                "success": False,
                "message": "考题生成失败",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }
