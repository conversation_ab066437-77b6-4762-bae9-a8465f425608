from enum import Enum
from typing import Dict, List, Optional
from pydantic import BaseModel


class APIVersion(str, Enum):
    """API版本枚举"""
    V1 = "v1"
    V2 = "v2"
    LATEST = "latest"


class APIStatus(str, Enum):
    """API状态枚举"""
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    BETA = "beta"
    ALPHA = "alpha"


class APIMetadata(BaseModel):
    """API元数据"""
    version: APIVersion
    status: APIStatus
    description: str
    tags: List[str]
    deprecated_in: Optional[APIVersion] = None
    removed_in: Optional[APIVersion] = None
    migration_guide: Optional[str] = None


class APIRegistry:
    """API注册表"""
    
    def __init__(self):
        self._versions: Dict[APIVersion, APIMetadata] = {}
        self._routes: Dict[str, Dict[APIVersion, str]] = {}
    
    def register_version(self, version: APIVersion, metadata: APIMetadata) -> None:
        """注册API版本"""
        self._versions[version] = metadata
    
    def register_route(self, route_name: str, version: APIVersion, path: str) -> None:
        """注册路由"""
        if route_name not in self._routes:
            self._routes[route_name] = {}
        self._routes[route_name][version] = path
    
    def get_version_info(self, version: APIVersion) -> Optional[APIMetadata]:
        """获取版本信息"""
        return self._versions.get(version)
    
    def get_route_path(self, route_name: str, version: APIVersion) -> Optional[str]:
        """获取路由路径"""
        return self._routes.get(route_name, {}).get(version)
    
    def get_all_versions(self) -> List[APIVersion]:
        """获取所有版本"""
        return list(self._versions.keys())
    
    def get_active_versions(self) -> List[APIVersion]:
        """获取活跃版本"""
        return [v for v, meta in self._versions.items() if meta.status == APIStatus.ACTIVE]
    
    def is_deprecated(self, version: APIVersion) -> bool:
        """检查版本是否已弃用"""
        metadata = self._versions.get(version)
        return metadata and metadata.status == APIStatus.DEPRECATED


# 全局API注册表
api_registry = APIRegistry()

# 注册版本信息
api_registry.register_version(
    APIVersion.V1,
    APIMetadata(
        version=APIVersion.V1,
        status=APIStatus.ACTIVE,
        description="第一个稳定版本的API",
        tags=["stable", "production"],
    )
)

api_registry.register_version(
    APIVersion.V2,
    APIMetadata(
        version=APIVersion.V2,
        status=APIStatus.BETA,
        description="第二个版本的API（测试版）",
        tags=["beta", "testing"],
    )
)