from sqlalchemy import text
from sqlalchemy.orm import Session

from app.shared.schemas.user import UserBase

_SQL_GET_USER_BY_ID = """SELECT id, name FROM ysh_base.users WHERE id = :id"""


class CRUDUser:
    async def get_user_by_id(self, db: Session, user_id: str):
        """根据ID获取用户"""
        result = db.execute(text(_SQL_GET_USER_BY_ID).bindparams(id=user_id))
        user = result.fetchone()
        if user:
            return UserBase(id=user[0], name=user[1])
        return None


user = CRUDUser()
