import logging
from typing import Any, Dict, List

from app.domain.ai.services.rerank.dashscope import rerank_documents
from app.shared.schemas.rerank import RerankApiResponse, RerankRequest, RerankServiceResponse

logger = logging.getLogger(__name__)


class RerankService:
    """重排序服务类"""

    @staticmethod
    def rerank(
        query: str,
        documents: List[str],
        model: str = "gte-rerank-v2",
        top_n: int = 5,
        return_documents: bool = True,
    ) -> RerankServiceResponse:
        """
        对文档进行重排序

        Args:
            query: 查询文本
            documents: 待排序的文档列表
            model: 使用的模型，默认为"gte-rerank-v2"
            top_n: 返回的排序结果数量，默认为5
            return_documents: 是否在结果中返回文档内容，默认为True

        Returns:
            RerankServiceResponse: 成功响应

        Raises:
            Exception: 当重排序失败时抛出异常
        """
        try:
            # 验证输入参数
            if not query.strip():
                raise ValueError("查询文本不能为空")

            if not documents:
                raise ValueError("文档列表不能为空")

            if top_n <= 0:
                raise ValueError("top_n必须大于0")

            # 调用DashScope API
            api_response = rerank_documents(
                query=query,
                documents=documents,
                model=model,
                top_n=top_n,
                return_documents=return_documents,
            )

            # 解析API响应
            parsed_response = RerankApiResponse(**api_response)

            # 构造服务响应
            service_response = RerankServiceResponse(
                query=query,
                results=parsed_response.output.results,
                total_results=len(parsed_response.output.results),
                model_used=model,
                request_id=parsed_response.request_id,
            )

            logger.info(
                f"重排序成功完成，查询: {query[:50]}..., "
                f"文档数量: {len(documents)}, 返回结果数量: {len(parsed_response.output.results)}"
            )

            return service_response

        except ValueError as e:
            logger.warning(f"重排序参数验证失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"重排序服务失败: {str(e)}")
            raise Exception(f"重排序服务失败: {str(e)}")

    @staticmethod
    def validate_request(request: RerankRequest) -> None:
        """
        验证重排序请求参数

        Args:
            request: 重排序请求对象

        Raises:
            ValueError: 当参数验证失败时抛出
        """
        if not request.query.strip():
            raise ValueError("查询文本不能为空")

        if not request.documents:
            raise ValueError("文档列表不能为空")

        if len(request.documents) > 1000:
            raise ValueError("文档数量不能超过1000个")

        if request.top_n <= 0 or request.top_n > len(request.documents):
            raise ValueError(f"top_n必须在1到{len(request.documents)}之间")

        # 检查文档内容长度
        for i, doc in enumerate(request.documents):
            if not doc.strip():
                raise ValueError(f"第{i + 1}个文档内容不能为空")
            if len(doc) > 4000:  # DashScope限制
                raise ValueError(f"第{i + 1}个文档长度超过4000字符限制")

    @staticmethod
    def get_top_documents(
        query: str, documents: List[str], top_n: int = 5, model: str = "gte-rerank-v2"
    ) -> List[Dict[str, Any]]:
        """
        获取最相关的文档（简化版本，只返回文档和分数）

        Args:
            query: 查询文本
            documents: 待排序的文档列表
            top_n: 返回的文档数量
            model: 使用的模型

        Returns:
            List[Dict[str, Any]]: 包含文档和分数的列表
        """
        try:
            response = RerankService.rerank(
                query=query,
                documents=documents,
                model=model,
                top_n=top_n,
                return_documents=True,
            )

            # 提取文档和分数
            top_docs = []
            for result in response.data.results:
                top_docs.append(
                    {
                        "text": result.document.text
                        if result.document
                        else documents[result.index],
                        "score": result.relevance_score,
                        "original_index": result.index,
                    }
                )

            return top_docs

        except Exception as e:
            logger.error(f"获取顶部文档失败: {str(e)}")
            raise
