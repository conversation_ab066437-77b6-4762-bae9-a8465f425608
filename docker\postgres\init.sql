-- PostgreSQL初始化脚本
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE biohub_dev'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'biohub_dev');

-- 设置数据库参数
ALTER DATABASE biohub_dev SET timezone TO 'UTC';
ALTER DATABASE biohub_dev SET default_text_search_config TO 'pg_catalog.english';

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'biohub') THEN
        CREATE USER biohub WITH PASSWORD 'biohub';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE biohub_dev TO biohub;

-- 连接到biohub_dev数据库
\c biohub_dev;

-- 创建schema
CREATE SCHEMA IF NOT EXISTS public;
GRANT ALL ON SCHEMA public TO biohub;

-- 创建扩展（在目标数据库中）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 设置默认权限
ALTER DEFAULT PRIVILEGES FOR USER postgres IN SCHEMA public GRANT ALL ON TABLES TO biohub;
ALTER DEFAULT PRIVILEGES FOR USER postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO biohub;
ALTER DEFAULT PRIVILEGES FOR USER postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO biohub;