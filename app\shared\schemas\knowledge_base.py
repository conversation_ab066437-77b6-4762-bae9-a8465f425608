from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MemberRole(str, Enum):
    """成员角色枚举"""

    OWNER = "owner"
    ADMIN = "admin"
    EDITOR = "editor"
    VIEWER = "viewer"


class KnowledgeBaseVisibility(str, Enum):
    """知识库可见性枚举"""

    PUBLIC = "public"  # 所有人可见
    PROTECTED = "protected"  # 根据成员管理分配权限
    PRIVATE = "private"  # 拥有者私有


# 知识库成员
class KnowledgeBaseMemberBase(BaseModel):
    """知识库成员基础模型"""

    user_id: str
    role: MemberRole = MemberRole.VIEWER


class KnowledgeBaseMemberCreate(KnowledgeBaseMemberBase):
    """知识库成员创建模型"""

    pass


class KnowledgeBaseMember(KnowledgeBaseMemberBase):
    """API响应中的知识库成员模型"""

    knowledge_base_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 知识库
class KnowledgeBaseBase(BaseModel):
    """知识库基础模型"""

    name: str
    description: Optional[str] = None
    visibility: KnowledgeBaseVisibility = KnowledgeBaseVisibility.PRIVATE
    metadata: Dict[str, Any] = Field(default_factory=dict)


class KnowledgeBaseCreate(KnowledgeBaseBase):
    """知识库创建模型"""

    pass


class KnowledgeBaseUpdate(KnowledgeBaseBase):
    """知识库更新模型"""

    name: Optional[str] = None


class KnowledgeBase(KnowledgeBaseBase):
    """API响应中的知识库模型"""

    id: int
    owner_id: int
    created_at: datetime
    updated_at: datetime
    document_count: int = 0

    class Config:
        from_attributes = True


# 文档
class DocumentBase(BaseModel):
    """文档基础模型"""

    title: str
    description: Optional[str] = None
    source: Optional[str] = None
    source_type: Optional[str] = None
    uploaded_by: Optional[str] = None  # 上传用户ID
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DocumentCreate(DocumentBase):
    """文档创建模型"""

    content: Optional[str] = None
    file_path: Optional[str] = None  # 用于上传文件


class DocumentUpdate(DocumentBase):
    """文档更新模型"""

    title: Optional[str] = None
    content: Optional[str] = None


class Document(DocumentBase):
    """API响应中的文档模型"""

    id: int
    knowledge_base_id: int
    uploaded_by: Optional[str] = None  # 上传用户ID
    uploaded_by_name: Optional[str] = None  # 上传用户名称
    created_at: datetime
    updated_at: datetime
    chunk_count: int = 0

    class Config:
        from_attributes = True


# 文档片段
class DocumentChunkBase(BaseModel):
    """文档片段基础模型"""

    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None


class DocumentChunkCreate(DocumentChunkBase):
    """文档片段创建模型"""

    pass


class DocumentChunkUpdate(DocumentChunkBase):
    """文档片段更新模型"""

    content: Optional[str] = None


class DocumentChunk(DocumentChunkBase):
    """API响应中的文档片段模型"""

    id: int
    document_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
