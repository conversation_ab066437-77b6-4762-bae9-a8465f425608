from sqlalchemy_utils import create_database, database_exists

from app.core.config.settings import settings
from app.infrastructure.database.session import Base, db_manager
from app.domain.knowledge_base.models.knowledge_base import (
    Document,
    DocumentChunk,
    KnowledgeBase,
    KnowledgeBaseMember,
)

from app.core.logging.log_config import get_logger
logger = get_logger("app.infrastructure.database.base")

def init_db():
    """初始化数据库表"""
    try:
        # 检查并创建PostgreSQL数据库
        postgres_url = settings.database.postgres_url
        logger.info(f"PostgreSQL URL: {postgres_url}")
        if not database_exists(postgres_url):
            create_database(postgres_url)
        
        # 检查并创建MySQL数据库
        mysql_url = settings.database.mysql_url
        logger.info(f"MySQL URL: {mysql_url}")
        if not database_exists(mysql_url):
            create_database(mysql_url)
        
        # 使用数据库管理器初始化所有表
        db_manager.init_databases()
        
    except Exception as e:
        from app.core.exceptions import DatabaseException
        raise DatabaseException(f"数据库初始化失败: {str(e)}") from e


def check_database_health():
    """检查数据库健康状态"""
    return db_manager.health_check()


def create_initial_data():
    """创建初始数据"""
    from app.infrastructure.database.tools import seeder_tool
    
    try:
        # 为PostgreSQL创建初始数据
        seeder_tool.seed_initial_data("postgres")
        
        # 为MySQL创建初始数据
        seeder_tool.seed_initial_data("mysql")
        
    except Exception as e:
        from app.core.exceptions import DatabaseException
        raise DatabaseException(f"创建初始数据失败: {str(e)}") from e
