from typing import Generator, Optional

from fastapi import Depends, HTTPException, Request, status
from redis import Redis

from app.core.config import settings
from app.core.exceptions.exceptions import AuthenticationException
from app.infrastructure.database.session import MySQLSessionLocal, PgSessionLocal, SessionLocal
from app.shared.schemas.user import UserBase


def get_pg_session() -> Generator:
    """获取PostgreSQL数据库会话"""
    with PgSessionLocal() as session:
        yield session


def get_mysql_session() -> Generator:
    """获取MySQL数据库会话"""
    with MySQLSessionLocal() as session:
        yield session


def get_session() -> Generator:
    """获取数据库会话（PostgreSQL）"""
    with PgSessionLocal() as session:
        yield session


def get_redis_client() -> Redis:
    """获取Redis客户端"""
    redis_clent: Redis = None

    if settings.REDIS_URL:
        redis_clent = Redis.from_url(settings.REDIS_URL)
    else:
        redis_clent = Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD,
            decode_responses=False,  # 保持原有的字节响应格式
        )
    return redis_clent


def get_current_user_id(request: Request) -> str:
    """获取当前用户ID"""
    user_id = getattr(request.state, "user_id", None)
    if not user_id:
        raise AuthenticationException("用户未认证")
    return user_id


def get_current_user(request: Request) -> UserBase:
    """获取当前用户"""
    user = getattr(request.state, "user", None)
    if not user:
        raise AuthenticationException("用户未认证")
    return user


def get_current_user_id_optional(request: Request) -> Optional[str]:
    """获取当前用户ID（可选）"""
    return getattr(request.state, "user_id", None)
