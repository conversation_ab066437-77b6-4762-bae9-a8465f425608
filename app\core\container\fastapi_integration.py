from typing import TypeVar, Type, Callable, Any
from fastapi import Depends

from app.core.container.container import container

T = TypeVar("T")


def get_service(service_key: str) -> Callable[[], Any]:
    """获取服务的FastAPI依赖函数"""
    def _get_service():
        return container.get(service_key)
    return _get_service


def inject_service(service_type: Type[T]) -> Callable[[], T]:
    """注入服务的FastAPI依赖函数"""
    service_key = service_type.__name__
    return get_service(service_key)


# 常用服务的FastAPI依赖
get_logger_factory = get_service("logger_factory")
get_cache_service = get_service("cache_service")
get_pg_engine = get_service("pg_engine")
get_mysql_engine = get_service("mysql_engine")
get_pg_session_factory = get_service("pg_session_factory")
get_mysql_session_factory = get_service("mysql_session_factory")


# 示例使用
def get_user_service():
    """获取用户服务"""
    return container.get("user_service")


def get_knowledge_base_service():
    """获取知识库服务"""
    return container.get("knowledge_base_service")