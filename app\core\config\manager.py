import os
from typing import Dict, Any, Optional
from pathlib import Path

from app.core.config.settings import Settings
from app.core.config.enums import Environment


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._settings: Optional[Settings] = None
        self._environment: Optional[Environment] = None
    
    def load_config(self, env_file: Optional[str] = None, environment: Optional[Environment] = None) -> Settings:
        """加载配置"""
        if env_file:
            # 确保环境文件存在
            if not Path(env_file).exists():
                raise FileNotFoundError(f"Environment file not found: {env_file}")
            
            # 设置环境变量
            os.environ.setdefault("ENV_FILE", env_file)
        
        if environment:
            os.environ.setdefault("ENVIRONMENT", environment.value)
        
        # 创建设置实例
        self._settings = Settings()
        self._environment = self._settings.app.ENVIRONMENT
        
        return self._settings
    
    def get_settings(self) -> Settings:
        """获取当前设置"""
        if self._settings is None:
            self._settings = self.load_config()
        return self._settings
    
    def reload_config(self) -> Settings:
        """重新加载配置"""
        self._settings = None
        return self.load_config()
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        settings = self.get_settings()
        return {
            "environment": settings.app.ENVIRONMENT,
            "is_production": settings.is_production,
            "is_development": settings.is_development,
            "is_testing": settings.is_testing,
            "debug": settings.app.DEBUG,
        }
    
    def validate_config(self) -> bool:
        """验证配置"""
        try:
            settings = self.get_settings()
            # 配置验证在 Settings 类初始化时自动进行
            return True
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        settings = self.get_settings()
        return settings.get_config_info()


# 全局配置管理器实例
config_manager = ConfigManager()