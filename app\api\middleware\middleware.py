import time
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.api.deps import get_mysql_session, get_redis_client
from app.core.security.auth import verify_token_from_request
from app.core.exceptions.exceptions import AuthenticationException
from app.core.logging.log_config import get_logger
from app.shared.utils.response_utils import unauthorized_response
from app.infrastructure.database.repositories.crud_user import user as crud_user


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件，用于记录请求和响应信息"""

    def __init__(self, app, logger_name: str = "app.middleware"):
        super().__init__(app)
        self.logger = get_logger(logger_name)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]

        # 记录请求开始时间
        start_time = time.time()

        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"

        # 记录请求信息
        self.logger.info(
            "请求开始",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": client_ip,
                "user_agent": request.headers.get("user-agent", "unknown"),
            },
        )

        # 将请求ID添加到请求状态中，供后续使用
        request.state.request_id = request_id

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 记录响应信息
            self.logger.info(
                "请求完成",
                extra={
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "process_time": f"{process_time:.3f}s",
                },
            )

            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id

            return response

        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time

            # 记录错误信息
            self.logger.error(
                f"请求处理异常: {str(e)}",
                extra={
                    "request_id": request_id,
                    "process_time": f"{process_time:.3f}s",
                    "exception_type": type(e).__name__,
                },
                exc_info=True,
            )

            # 重新抛出异常
            raise


class RequestContextMiddleware(BaseHTTPMiddleware):
    """请求上下文中间件，用于在整个请求生命周期中传递请求ID"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 从请求状态中获取请求ID，如果没有则生成一个
        request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])

        # 设置请求上下文
        import contextvars

        request_id_var = contextvars.ContextVar("request_id", default=request_id)
        request_id_var.set(request_id)

        # 处理请求
        response = await call_next(request)

        return response


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """认证中间件，用于验证用户身份"""

    def __init__(self, app, logger_name: str = "app.auth_middleware"):
        super().__init__(app)
        self.logger = get_logger(logger_name)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取当前请求的URI
        uri = request.url.path

        # 如果URI中包含'no-auth'，则跳过认证直接处理请求
        if "no-auth" in uri:
            self.logger.debug(f"跳过认证: {uri}")
            response = await call_next(request)
            return response
        # 如果是openapi.json请求，跳过认证
        if uri.endswith("/openapi.json"):
            self.logger.debug(f"跳过认证: {uri}")
            response = await call_next(request)
            return response
        try:
            # 获取Redis客户端
            redis_client = get_redis_client()

            # 验证token并获取用户ID
            user_id = await verify_token_from_request(request, redis_client)

            # 将用户ID存储到请求状态中
            if user_id:
                request.state.user_id = user_id
                self.logger.debug(f"用户认证成功: {user_id}")
                try:
                    # 获取MySQL会话
                    db = next(get_mysql_session())
                    current_user = await crud_user.get_user_by_id(
                        user_id=user_id, db=db
                    )
                    request.state.user = current_user
                except Exception as e:
                    self.logger.error(
                        f"封装用户信息到 request.state 失败,user_id = {user_id},error = {e}",
                        exc_info=True,
                    )
            # 处理请求
            response = await call_next(request)
            return response

        except AuthenticationException as e:
            self.logger.warning(f"认证失败: {str(e)}")
            # 对于认证失败，直接抛出异常
            return unauthorized_response(msg=str(e))
        except Exception as e:
            raise e
