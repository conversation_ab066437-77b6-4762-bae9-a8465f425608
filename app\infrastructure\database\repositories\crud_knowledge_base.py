from typing import List, Optional

from sqlalchemy.orm import Session

from app.infrastructure.database.repositories.base import CRUDBase
from app.domain.knowledge_base.models.knowledge_base import (
    Document,
    DocumentChunk,
    KnowledgeBase,
    KnowledgeBaseMember,
)
from app.shared.schemas.knowledge_base import (
    DocumentChunkCreate,
    DocumentChunkUpdate,
    DocumentCreate,
    DocumentUpdate,
    KnowledgeBaseCreate,
    KnowledgeBaseMemberCreate,
    KnowledgeBaseUpdate,
)


class CRUDKnowledgeBase(
    CRUDBase[KnowledgeBase, KnowledgeBaseCreate, KnowledgeBaseUpdate]
):
    """知识库CRUD操作类"""

    async def create_with_owner(
        self, db: Session, *, obj_in: KnowledgeBaseCreate, owner_id: int
    ) -> KnowledgeBase:
        """创建知识库"""
        db_obj = KnowledgeBase(
            name=obj_in.name,
            description=obj_in.description,
            visibility=obj_in.visibility,
            metadata=obj_in.metadata,
            owner_id=owner_id,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)

        # 创建所有者成员记录
        member = KnowledgeBaseMember(
            knowledge_base_id=db_obj.id, user_id=owner_id, role="owner"
        )
        db.add(member)
        db.commit()

        return db_obj

    async def get_multi_by_owner(
        self, db: Session, *, owner_id: int, skip: int = 0, limit: int = 100
    ) -> List[KnowledgeBase]:
        """获取用户拥有的知识库"""
        return (
            db.query(KnowledgeBase)
            .filter(KnowledgeBase.owner_id == owner_id)
            .order_by(KnowledgeBase.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_multi_by_member(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[KnowledgeBase]:
        """获取用户作为成员的知识库"""
        return (
            db.query(KnowledgeBase)
            .join(
                KnowledgeBaseMember,
                KnowledgeBaseMember.knowledge_base_id == KnowledgeBase.id,
            )
            .filter(KnowledgeBaseMember.user_id == user_id)
            .order_by(KnowledgeBase.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_public(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[KnowledgeBase]:
        """获取公开知识库"""
        return (
            db.query(KnowledgeBase)
            .filter(KnowledgeBase.visibility == "public")
            .order_by(KnowledgeBase.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def add_member(
        self, db: Session, *, knowledge_base_id: int, obj_in: KnowledgeBaseMemberCreate
    ) -> KnowledgeBaseMember:
        """添加知识库成员"""
        db_obj = KnowledgeBaseMember(
            knowledge_base_id=knowledge_base_id,
            user_id=obj_in.user_id,
            role=obj_in.role,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def remove_member(
        self, db: Session, *, knowledge_base_id: int, user_id: int
    ) -> Optional[KnowledgeBaseMember]:
        """移除知识库成员"""
        obj = (
            db.query(KnowledgeBaseMember)
            .filter(
                KnowledgeBaseMember.knowledge_base_id == knowledge_base_id,
                KnowledgeBaseMember.user_id == user_id,
                KnowledgeBaseMember.role != "owner",  # 不能移除所有者
            )
            .first()
        )
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    async def get_members(
        self, db: Session, *, knowledge_base_id: int, skip: int = 0, limit: int = 100
    ) -> List[KnowledgeBaseMember]:
        """获取知识库成员"""
        return (
            db.query(KnowledgeBaseMember)
            .filter(KnowledgeBaseMember.knowledge_base_id == knowledge_base_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def is_member(
        self, db: Session, *, knowledge_base_id: int, user_id: int
    ) -> bool:
        """检查用户是否是知识库成员"""
        return (
            db.query(KnowledgeBaseMember)
            .filter(
                KnowledgeBaseMember.knowledge_base_id == knowledge_base_id,
                KnowledgeBaseMember.user_id == user_id,
            )
            .first()
        ) is not None

    async def get_member_role(
        self, db: Session, *, knowledge_base_id: int, user_id: int
    ) -> Optional[str]:
        """获取用户在知识库中的角色"""
        member = (
            db.query(KnowledgeBaseMember)
            .filter(
                KnowledgeBaseMember.knowledge_base_id == knowledge_base_id,
                KnowledgeBaseMember.user_id == user_id,
            )
            .first()
        )
        return member.role if member else None

    async def is_admin_member(
        self, db: Session, *, knowledge_base_id: int, user_id: int
    ) -> bool:
        """检查用户是否是知识库的管理员成员"""
        member = (
            db.query(KnowledgeBaseMember)
            .filter(
                KnowledgeBaseMember.knowledge_base_id == knowledge_base_id,
                KnowledgeBaseMember.user_id == user_id,
                KnowledgeBaseMember.role == "admin",
            )
            .first()
        )
        return member is not None


class CRUDDocument(CRUDBase[Document, DocumentCreate, DocumentUpdate]):
    """文档CRUD操作类"""

    async def create_with_knowledge_base(
        self, db: Session, *, obj_in: DocumentCreate, knowledge_base_id: int
    ) -> Document:
        """创建文档"""
        db_obj = Document(
            title=obj_in.title,
            description=obj_in.description,
            source=obj_in.source,
            source_type=obj_in.source_type,
            uploaded_by=obj_in.uploaded_by,
            metadata=obj_in.metadata,
            knowledge_base_id=knowledge_base_id,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)

        return db_obj

    async def create_with_file(
        self,
        db: Session,
        *,
        knowledge_base_id: int,
        file_name: str,
        content_type: str,
        file_content: bytes,
    ) -> Document:
        """创建文档并保存文件内容"""
        # TODO: 将文件内容保存到文件系统或对象存储中，并记录文件路径
        # For now, let's just create a dummy file path
        file_path = f"/app/data/knowledge_bases/{knowledge_base_id}/{file_name}"

        # Save the file content to the dummy path (in a real app, this would be persistent storage)
        # This is a placeholder for actual file storage logic
        with open(file_path, "wb") as f:
            f.write(file_content)

        db_obj = Document(
            title=file_name,
            description=f"Uploaded document: {file_name}",
            file_path=file_path,
            content_type=content_type,
            knowledge_base_id=knowledge_base_id,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def get_multi_by_knowledge_base(
        self, db: Session, *, knowledge_base_id: int, skip: int = 0, limit: int = 100
    ) -> List[Document]:
        """获取知识库的多个文档"""
        return (
            db.query(Document)
            .filter(Document.knowledge_base_id == knowledge_base_id)
            .order_by(Document.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_by_id_and_knowledge_base(
        self, db: Session, *, id: int, knowledge_base_id: int
    ) -> Optional[Document]:
        """通过ID和知识库ID获取文档"""
        return (
            db.query(Document)
            .filter(Document.id == id, Document.knowledge_base_id == knowledge_base_id)
            .first()
        )


class CRUDDocumentChunk(
    CRUDBase[DocumentChunk, DocumentChunkCreate, DocumentChunkUpdate]
):
    """文档片段CRUD操作类"""

    async def get_multi_by_document(
        self, db: Session, *, document_id: int, skip: int = 0, limit: int = 100
    ) -> List[DocumentChunk]:
        """获取文档的多个片段"""
        return (
            db.query(DocumentChunk)
            .filter(DocumentChunk.document_id == document_id)
            .order_by(DocumentChunk.chunk_index.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )


knowledge_base = CRUDKnowledgeBase(KnowledgeBase)
document = CRUDDocument(Document)
document_chunk = CRUDDocumentChunk(DocumentChunk)
