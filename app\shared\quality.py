"""
代码质量工具
提供代码质量检查、分析和改进建议
"""

import ast
import inspect
import re
from typing import Any, Dict, List, Optional, Tuple, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from app.shared.docs import validate_docstring


class QualityLevel(Enum):
    """代码质量级别"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"


class IssueType(Enum):
    """问题类型"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    SUGGESTION = "suggestion"


@dataclass
class QualityIssue:
    """质量问题"""
    type: IssueType
    message: str
    line: Optional[int] = None
    column: Optional[int] = None
    severity: int = 1  # 1-5, 5最严重
    category: str = "general"
    fix_suggestion: Optional[str] = None


@dataclass
class QualityReport:
    """质量报告"""
    overall_score: float
    quality_level: QualityLevel
    issues: List[QualityIssue] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    suggestions: List[str] = field(default_factory=list)


class CodeComplexityAnalyzer:
    """代码复杂度分析器"""
    
    @staticmethod
    def calculate_cyclomatic_complexity(func: Callable) -> int:
        """
        计算函数的圈复杂度
        
        Args:
            func: 要分析的函数
        
        Returns:
            int: 圈复杂度值
        
        Examples:
            >>> def simple_func():
            ...     return True
            >>> complexity = CodeComplexityAnalyzer.calculate_cyclomatic_complexity(simple_func)
            >>> print(complexity)
            1
        """
        try:
            source = inspect.getsource(func)
            tree = ast.parse(source)
            
            complexity = 1  # 基础复杂度
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                    complexity += 1
                elif isinstance(node, ast.ExceptHandler):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    # and/or 操作符
                    complexity += len(node.values) - 1
                elif isinstance(node, ast.comprehension):
                    # 列表推导式
                    complexity += 1
            
            return complexity
        except Exception:
            return 1
    
    @staticmethod
    def calculate_function_length(func: Callable) -> int:
        """
        计算函数长度（行数）
        
        Args:
            func: 要分析的函数
        
        Returns:
            int: 函数行数
        """
        try:
            source = inspect.getsource(func)
            return len(source.splitlines())
        except Exception:
            return 0
    
    @staticmethod
    def calculate_parameter_count(func: Callable) -> int:
        """
        计算函数参数数量
        
        Args:
            func: 要分析的函数
        
        Returns:
            int: 参数数量
        """
        try:
            sig = inspect.signature(func)
            return len(sig.parameters)
        except Exception:
            return 0


class NamingConventionChecker:
    """命名规范检查器"""
    
    # 命名规范正则表达式
    SNAKE_CASE = re.compile(r'^[a-z_][a-z0-9_]*$')
    CAMEL_CASE = re.compile(r'^[a-z][a-zA-Z0-9]*$')
    PASCAL_CASE = re.compile(r'^[A-Z][a-zA-Z0-9]*$')
    UPPER_CASE = re.compile(r'^[A-Z_][A-Z0-9_]*$')
    
    @staticmethod
    def check_function_name(name: str) -> bool:
        """
        检查函数名是否符合snake_case规范
        
        Args:
            name: 函数名
        
        Returns:
            bool: 是否符合规范
        """
        return bool(NamingConventionChecker.SNAKE_CASE.match(name))
    
    @staticmethod
    def check_class_name(name: str) -> bool:
        """
        检查类名是否符合PascalCase规范
        
        Args:
            name: 类名
        
        Returns:
            bool: 是否符合规范
        """
        return bool(NamingConventionChecker.PASCAL_CASE.match(name))
    
    @staticmethod
    def check_variable_name(name: str) -> bool:
        """
        检查变量名是否符合snake_case规范
        
        Args:
            name: 变量名
        
        Returns:
            bool: 是否符合规范
        """
        return bool(NamingConventionChecker.SNAKE_CASE.match(name))
    
    @staticmethod
    def check_constant_name(name: str) -> bool:
        """
        检查常量名是否符合UPPER_CASE规范
        
        Args:
            name: 常量名
        
        Returns:
            bool: 是否符合规范
        """
        return bool(NamingConventionChecker.UPPER_CASE.match(name))


class CodeQualityAnalyzer:
    """代码质量分析器"""
    
    def __init__(self):
        self.complexity_analyzer = CodeComplexityAnalyzer()
        self.naming_checker = NamingConventionChecker()
    
    def analyze_function(self, func: Callable) -> QualityReport:
        """
        分析函数质量
        
        Args:
            func: 要分析的函数
        
        Returns:
            QualityReport: 质量报告
        
        Examples:
            >>> def example_func(a: int, b: int) -> int:
            ...     '''Add two numbers'''
            ...     return a + b
            >>> analyzer = CodeQualityAnalyzer()
            >>> report = analyzer.analyze_function(example_func)
            >>> print(report.quality_level)
            QualityLevel.GOOD
        """
        report = QualityReport(overall_score=0.0, quality_level=QualityLevel.POOR)
        
        # 检查函数名规范
        if not self.naming_checker.check_function_name(func.__name__):
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Function name '{func.__name__}' does not follow snake_case convention",
                category="naming",
                severity=2,
                fix_suggestion="Use snake_case for function names (e.g., 'my_function')"
            ))
        
        # 检查文档字符串
        doc_issues = validate_docstring(func)
        for issue in doc_issues:
            report.issues.append(QualityIssue(
                type=IssueType.INFO,
                message=issue,
                category="documentation",
                severity=1,
                fix_suggestion="Add proper docstring with parameter and return documentation"
            ))
        
        # 检查复杂度
        complexity = self.complexity_analyzer.calculate_cyclomatic_complexity(func)
        report.metrics['cyclomatic_complexity'] = complexity
        
        if complexity > 10:
            report.issues.append(QualityIssue(
                type=IssueType.ERROR,
                message=f"Cyclomatic complexity is {complexity} (recommended: <= 10)",
                category="complexity",
                severity=4,
                fix_suggestion="Consider breaking down the function into smaller functions"
            ))
        elif complexity > 5:
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Cyclomatic complexity is {complexity} (recommended: <= 5)",
                category="complexity",
                severity=2,
                fix_suggestion="Consider simplifying the function logic"
            ))
        
        # 检查函数长度
        length = self.complexity_analyzer.calculate_function_length(func)
        report.metrics['function_length'] = length
        
        if length > 50:
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Function is {length} lines long (recommended: <= 50)",
                category="length",
                severity=2,
                fix_suggestion="Consider breaking down the function into smaller functions"
            ))
        
        # 检查参数数量
        param_count = self.complexity_analyzer.calculate_parameter_count(func)
        report.metrics['parameter_count'] = param_count
        
        if param_count > 5:
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Function has {param_count} parameters (recommended: <= 5)",
                category="parameters",
                severity=2,
                fix_suggestion="Consider using a data class or dictionary for multiple parameters"
            ))
        
        # 检查类型注解
        try:
            sig = inspect.signature(func)
            untyped_params = []
            for param_name, param in sig.parameters.items():
                if param.annotation == inspect.Parameter.empty:
                    untyped_params.append(param_name)
            
            if untyped_params:
                report.issues.append(QualityIssue(
                    type=IssueType.INFO,
                    message=f"Parameters without type annotations: {', '.join(untyped_params)}",
                    category="type_hints",
                    severity=1,
                    fix_suggestion="Add type annotations to improve code clarity"
                ))
            
            if sig.return_annotation == inspect.Signature.empty:
                report.issues.append(QualityIssue(
                    type=IssueType.INFO,
                    message="Function missing return type annotation",
                    category="type_hints",
                    severity=1,
                    fix_suggestion="Add return type annotation"
                ))
        except Exception:
            pass
        
        # 计算总体得分
        report.overall_score = self._calculate_score(report)
        report.quality_level = self._determine_quality_level(report.overall_score)
        
        return report
    
    def analyze_class(self, cls: Type) -> QualityReport:
        """
        分析类质量
        
        Args:
            cls: 要分析的类
        
        Returns:
            QualityReport: 质量报告
        """
        report = QualityReport(overall_score=0.0, quality_level=QualityLevel.POOR)
        
        # 检查类名规范
        if not self.naming_checker.check_class_name(cls.__name__):
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Class name '{cls.__name__}' does not follow PascalCase convention",
                category="naming",
                severity=2,
                fix_suggestion="Use PascalCase for class names (e.g., 'MyClass')"
            ))
        
        # 检查类文档字符串
        if not cls.__doc__:
            report.issues.append(QualityIssue(
                type=IssueType.INFO,
                message="Class missing docstring",
                category="documentation",
                severity=1,
                fix_suggestion="Add class docstring describing its purpose"
            ))
        
        # 分析类的方法
        methods = inspect.getmembers(cls, predicate=inspect.isfunction)
        report.metrics['method_count'] = len(methods)
        
        method_complexities = []
        for method_name, method in methods:
            if not method_name.startswith('_'):  # 跳过私有方法
                complexity = self.complexity_analyzer.calculate_cyclomatic_complexity(method)
                method_complexities.append(complexity)
        
        if method_complexities:
            report.metrics['avg_method_complexity'] = sum(method_complexities) / len(method_complexities)
            report.metrics['max_method_complexity'] = max(method_complexities)
        
        # 检查方法数量
        if len(methods) > 20:
            report.issues.append(QualityIssue(
                type=IssueType.WARNING,
                message=f"Class has {len(methods)} methods (recommended: <= 20)",
                category="design",
                severity=2,
                fix_suggestion="Consider breaking down the class into smaller classes"
            ))
        
        # 计算总体得分
        report.overall_score = self._calculate_score(report)
        report.quality_level = self._determine_quality_level(report.overall_score)
        
        return report
    
    def _calculate_score(self, report: QualityReport) -> float:
        """
        计算质量得分
        
        Args:
            report: 质量报告
        
        Returns:
            float: 质量得分 (0-100)
        """
        base_score = 100.0
        
        # 根据问题严重程度扣分
        for issue in report.issues:
            if issue.type == IssueType.ERROR:
                base_score -= issue.severity * 10
            elif issue.type == IssueType.WARNING:
                base_score -= issue.severity * 5
            elif issue.type == IssueType.INFO:
                base_score -= issue.severity * 2
        
        # 确保分数在0-100范围内
        return max(0.0, min(100.0, base_score))
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """
        根据得分确定质量级别
        
        Args:
            score: 质量得分
        
        Returns:
            QualityLevel: 质量级别
        """
        if score >= 90:
            return QualityLevel.EXCELLENT
        elif score >= 70:
            return QualityLevel.GOOD
        elif score >= 50:
            return QualityLevel.FAIR
        else:
            return QualityLevel.POOR


class CodeFormatter:
    """代码格式化器"""
    
    @staticmethod
    def format_docstring(docstring: str, max_width: int = 79) -> str:
        """
        格式化文档字符串
        
        Args:
            docstring: 原始文档字符串
            max_width: 最大宽度
        
        Returns:
            str: 格式化后的文档字符串
        """
        if not docstring:
            return ""
        
        lines = docstring.strip().split('\n')
        formatted_lines = []
        
        for line in lines:
            stripped = line.strip()
            if len(stripped) <= max_width:
                formatted_lines.append(stripped)
            else:
                # 简单的换行处理
                words = stripped.split()
                current_line = ""
                for word in words:
                    if len(current_line + " " + word) <= max_width:
                        current_line += " " + word if current_line else word
                    else:
                        formatted_lines.append(current_line)
                        current_line = word
                if current_line:
                    formatted_lines.append(current_line)
        
        return '\n'.join(formatted_lines)
    
    @staticmethod
    def suggest_improvements(report: QualityReport) -> List[str]:
        """
        根据质量报告提供改进建议
        
        Args:
            report: 质量报告
        
        Returns:
            List[str]: 改进建议列表
        """
        suggestions = []
        
        # 根据问题类型提供建议
        categories = {}
        for issue in report.issues:
            if issue.category not in categories:
                categories[issue.category] = []
            categories[issue.category].append(issue)
        
        if 'complexity' in categories:
            suggestions.append("Consider reducing cyclomatic complexity by:")
            suggestions.append("  - Breaking down complex functions into smaller ones")
            suggestions.append("  - Using guard clauses to reduce nesting")
            suggestions.append("  - Extracting complex conditions into separate functions")
        
        if 'naming' in categories:
            suggestions.append("Improve naming conventions by:")
            suggestions.append("  - Using snake_case for functions and variables")
            suggestions.append("  - Using PascalCase for class names")
            suggestions.append("  - Using UPPER_CASE for constants")
        
        if 'documentation' in categories:
            suggestions.append("Enhance documentation by:")
            suggestions.append("  - Adding docstrings to all public functions and classes")
            suggestions.append("  - Including parameter and return type descriptions")
            suggestions.append("  - Providing usage examples")
        
        if 'type_hints' in categories:
            suggestions.append("Add type hints for better code clarity and IDE support")
        
        return suggestions


def quality_check(func: Callable) -> Callable:
    """
    质量检查装饰器
    
    Args:
        func: 要检查的函数
    
    Returns:
        Callable: 装饰后的函数
    
    Examples:
        >>> @quality_check
        ... def example_func(a: int, b: int) -> int:
        ...     '''Add two numbers'''
        ...     return a + b
    """
    analyzer = CodeQualityAnalyzer()
    report = analyzer.analyze_function(func)
    
    # 如果有严重问题，打印警告
    errors = [issue for issue in report.issues if issue.type == IssueType.ERROR]
    if errors:
        print(f"Quality check for {func.__name__}:")
        for error in errors:
            print(f"  ERROR: {error.message}")
    
    return func


# 导出所有质量工具
__all__ = [
    'QualityLevel',
    'IssueType',
    'QualityIssue',
    'QualityReport',
    'CodeComplexityAnalyzer',
    'NamingConventionChecker',
    'CodeQualityAnalyzer',
    'CodeFormatter',
    'quality_check',
]