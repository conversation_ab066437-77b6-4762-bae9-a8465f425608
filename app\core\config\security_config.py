from typing import Dict, Any, Optional
from pydantic import field_validator

from app.core.config.base import BaseConfig


class SecurityConfig(BaseConfig):
    """安全配置"""
    
    # 认证配置
    AUTH_SERVICE_URL: str = "http://auth-service:8080/api"
    JWT_SECRET_KEY: Optional[str] = None
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30
    
    # 加密配置
    SECRET_KEY: Optional[str] = None
    ENCRYPT_KEY: Optional[str] = None
    
    # 安全头配置
    SECURITY_HEADERS: bool = True
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # 秒
    
    # SSL配置
    SSL_ENABLED: bool = False
    SSL_CERT_PATH: Optional[str] = None
    SSL_KEY_PATH: Optional[str] = None
    
    @field_validator("JWT_EXPIRE_MINUTES")
    def validate_jwt_expire(cls, v):
        if v < 1:
            raise ValueError("JWT expiration must be at least 1 minute")
        return v
    
    @field_validator("RATE_LIMIT_REQUESTS")
    def validate_rate_limit(cls, v):
        if v < 1:
            raise ValueError("Rate limit requests must be at least 1")
        return v
    
    def validate_config(self) -> None:
        """验证配置"""
        if self.SSL_ENABLED:
            if not self.SSL_CERT_PATH or not self.SSL_KEY_PATH:
                raise ValueError("SSL certificate and key paths are required when SSL is enabled")
        
        if self.JWT_SECRET_KEY and len(self.JWT_SECRET_KEY) < 32:
            raise ValueError("JWT secret key must be at least 32 characters long")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息（不包含敏感信息）"""
        return {
            "auth_service_url": self.AUTH_SERVICE_URL,
            "jwt_algorithm": self.JWT_ALGORITHM,
            "jwt_expire_minutes": self.JWT_EXPIRE_MINUTES,
            "security_headers": self.SECURITY_HEADERS,
            "rate_limit_enabled": self.RATE_LIMIT_ENABLED,
            "rate_limit_requests": self.RATE_LIMIT_REQUESTS,
            "rate_limit_window": self.RATE_LIMIT_WINDOW,
            "ssl_enabled": self.SSL_ENABLED,
        }