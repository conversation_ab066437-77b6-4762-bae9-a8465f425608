from typing import List

from app.core.container.container import Container, ServiceProvider
from app.core.container.providers.database import DatabaseServiceProvider
from app.core.container.providers.logging import LoggingServiceProvider
from app.core.container.providers.cache import CacheServiceProvider


class ServiceBootstrap:
    """服务启动器"""
    
    def __init__(self, container: Container):
        self.container = container
        self.providers: List[ServiceProvider] = []
    
    def add_provider(self, provider: ServiceProvider) -> None:
        """添加服务提供者"""
        self.providers.append(provider)
    
    def register_all(self) -> None:
        """注册所有服务"""
        for provider in self.providers:
            provider.register(self.container)
    
    def get_default_providers(self) -> List[ServiceProvider]:
        """获取默认服务提供者"""
        return [
            DatabaseServiceProvider(),
            LoggingServiceProvider(),
            CacheServiceProvider(),
        ]
    
    def bootstrap(self) -> None:
        """启动服务"""
        # 添加默认提供者
        for provider in self.get_default_providers():
            self.add_provider(provider)
        
        # 注册所有服务
        self.register_all()


def bootstrap_container(container: Container) -> None:
    """启动容器"""
    bootstrap = ServiceBootstrap(container)
    bootstrap.bootstrap()