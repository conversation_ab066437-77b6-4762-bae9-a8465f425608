from typing import TypeVar, Generic, Optional, List, Dict, Any, Type, Union
from abc import ABC, abstractmethod
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.sql import Select

from app.core.exceptions import DatabaseException, ResourceNotFoundException
from app.core.exceptions.utils import transaction_context

ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """基础仓储类"""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    @abstractmethod
    def get_session(self) -> Session:
        """获取数据库会话"""
        pass
    
    def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        try:
            with self.get_session() as db:
                return db.query(self.model).filter(self.model.id == id).first()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查询{self.model.__name__}失败: {str(e)}") from e
    
    def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[List[str]] = None
    ) -> List[ModelType]:
        """获取多个对象"""
        try:
            with self.get_session() as db:
                query = db.query(self.model)
                
                # 应用过滤器
                if filters:
                    query = self._apply_filters(query, filters)
                
                # 应用排序
                if order_by:
                    query = self._apply_ordering(query, order_by)
                
                return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查询{self.model.__name__}列表失败: {str(e)}") from e
    
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """计数"""
        try:
            with self.get_session() as db:
                query = db.query(func.count(self.model.id))
                
                if filters:
                    query = self._apply_filters(query, filters)
                
                return query.scalar()
        except SQLAlchemyError as e:
            raise DatabaseException(f"计数{self.model.__name__}失败: {str(e)}") from e
    
    def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        try:
            with transaction_context(self.get_session()) as db:
                if hasattr(obj_in, 'dict'):
                    obj_data = obj_in.dict()
                else:
                    obj_data = obj_in
                
                db_obj = self.model(**obj_data)
                db.add(db_obj)
                db.flush()
                db.refresh(db_obj)
                return db_obj
        except IntegrityError as e:
            raise DatabaseException(f"创建{self.model.__name__}失败，违反唯一约束: {str(e)}") from e
        except SQLAlchemyError as e:
            raise DatabaseException(f"创建{self.model.__name__}失败: {str(e)}") from e
    
    def update(self, id: Any, obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """更新对象"""
        try:
            with transaction_context(self.get_session()) as db:
                db_obj = db.query(self.model).filter(self.model.id == id).first()
                if not db_obj:
                    raise ResourceNotFoundException(f"{self.model.__name__} {id} 不存在")
                
                if hasattr(obj_in, 'dict'):
                    update_data = obj_in.dict(exclude_unset=True)
                else:
                    update_data = obj_in
                
                for field, value in update_data.items():
                    if hasattr(db_obj, field):
                        setattr(db_obj, field, value)
                
                db.flush()
                db.refresh(db_obj)
                return db_obj
        except SQLAlchemyError as e:
            raise DatabaseException(f"更新{self.model.__name__}失败: {str(e)}") from e
    
    def delete(self, id: Any) -> bool:
        """删除对象"""
        try:
            with transaction_context(self.get_session()) as db:
                db_obj = db.query(self.model).filter(self.model.id == id).first()
                if not db_obj:
                    raise ResourceNotFoundException(f"{self.model.__name__} {id} 不存在")
                
                db.delete(db_obj)
                db.flush()
                return True
        except SQLAlchemyError as e:
            raise DatabaseException(f"删除{self.model.__name__}失败: {str(e)}") from e
    
    def exists(self, id: Any) -> bool:
        """检查对象是否存在"""
        try:
            with self.get_session() as db:
                return db.query(self.model).filter(self.model.id == id).first() is not None
        except SQLAlchemyError as e:
            raise DatabaseException(f"检查{self.model.__name__}存在性失败: {str(e)}") from e
    
    def find_by(self, **filters) -> List[ModelType]:
        """根据条件查找"""
        try:
            with self.get_session() as db:
                query = db.query(self.model)
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        query = query.filter(getattr(self.model, key) == value)
                return query.all()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查找{self.model.__name__}失败: {str(e)}") from e
    
    def find_one_by(self, **filters) -> Optional[ModelType]:
        """根据条件查找单个对象"""
        try:
            with self.get_session() as db:
                query = db.query(self.model)
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        query = query.filter(getattr(self.model, key) == value)
                return query.first()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查找{self.model.__name__}失败: {str(e)}") from e
    
    def _apply_filters(self, query, filters: Dict[str, Any]):
        """应用过滤器"""
        for key, value in filters.items():
            if hasattr(self.model, key):
                if isinstance(value, list):
                    query = query.filter(getattr(self.model, key).in_(value))
                elif isinstance(value, dict):
                    if 'gte' in value:
                        query = query.filter(getattr(self.model, key) >= value['gte'])
                    if 'lte' in value:
                        query = query.filter(getattr(self.model, key) <= value['lte'])
                    if 'gt' in value:
                        query = query.filter(getattr(self.model, key) > value['gt'])
                    if 'lt' in value:
                        query = query.filter(getattr(self.model, key) < value['lt'])
                    if 'like' in value:
                        query = query.filter(getattr(self.model, key).like(f"%{value['like']}%"))
                else:
                    query = query.filter(getattr(self.model, key) == value)
        return query
    
    def _apply_ordering(self, query, order_by: List[str]):
        """应用排序"""
        for order in order_by:
            if order.startswith('-'):
                field = order[1:]
                if hasattr(self.model, field):
                    query = query.order_by(desc(getattr(self.model, field)))
            else:
                if hasattr(self.model, order):
                    query = query.order_by(asc(getattr(self.model, order)))
        return query


class AsyncBaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """异步基础仓储类"""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    @abstractmethod
    async def get_session(self) -> AsyncSession:
        """获取异步数据库会话"""
        pass
    
    async def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        try:
            async with self.get_session() as db:
                result = await db.execute(
                    db.query(self.model).filter(self.model.id == id)
                )
                return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查询{self.model.__name__}失败: {str(e)}") from e
    
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[List[str]] = None
    ) -> List[ModelType]:
        """获取多个对象"""
        try:
            async with self.get_session() as db:
                query = db.query(self.model)
                
                if filters:
                    query = self._apply_filters(query, filters)
                
                if order_by:
                    query = self._apply_ordering(query, order_by)
                
                result = await db.execute(query.offset(skip).limit(limit))
                return result.scalars().all()
        except SQLAlchemyError as e:
            raise DatabaseException(f"查询{self.model.__name__}列表失败: {str(e)}") from e
    
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        try:
            async with self.get_session() as db:
                if hasattr(obj_in, 'dict'):
                    obj_data = obj_in.dict()
                else:
                    obj_data = obj_in
                
                db_obj = self.model(**obj_data)
                db.add(db_obj)
                await db.commit()
                await db.refresh(db_obj)
                return db_obj
        except IntegrityError as e:
            raise DatabaseException(f"创建{self.model.__name__}失败，违反唯一约束: {str(e)}") from e
        except SQLAlchemyError as e:
            raise DatabaseException(f"创建{self.model.__name__}失败: {str(e)}") from e
    
    def _apply_filters(self, query, filters: Dict[str, Any]):
        """应用过滤器"""
        for key, value in filters.items():
            if hasattr(self.model, key):
                if isinstance(value, list):
                    query = query.filter(getattr(self.model, key).in_(value))
                elif isinstance(value, dict):
                    if 'gte' in value:
                        query = query.filter(getattr(self.model, key) >= value['gte'])
                    if 'lte' in value:
                        query = query.filter(getattr(self.model, key) <= value['lte'])
                    if 'like' in value:
                        query = query.filter(getattr(self.model, key).like(f"%{value['like']}%"))
                else:
                    query = query.filter(getattr(self.model, key) == value)
        return query
    
    def _apply_ordering(self, query, order_by: List[str]):
        """应用排序"""
        for order in order_by:
            if order.startswith('-'):
                field = order[1:]
                if hasattr(self.model, field):
                    query = query.order_by(desc(getattr(self.model, field)))
            else:
                if hasattr(self.model, order):
                    query = query.order_by(asc(getattr(self.model, order)))
        return query