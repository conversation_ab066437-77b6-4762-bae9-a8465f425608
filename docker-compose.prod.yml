version: '3.8'

services:
  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    environment:
      - APP__ENVIRONMENT=production
      - APP__DEBUG=false
      - APP__WORKERS=4
      - DATABASE__POSTGRES_SERVER=postgres
      - DATABASE__POSTGRES_USER=postgres
      - DATABASE__POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE__POSTGRES_DB=biohub_prod
      - DATABASE__POSTGRES_PORT=5432
      - DATABASE__MYSQL_SERVER=mysql
      - DATABASE__MYSQL_USER=root
      - DATABASE__MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - DATABASE__MYSQL_DB=biohub_prod
      - DATABASE__MYSQL_PORT=3306
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_API_KEY=${ELASTICSEARCH_API_KEY}
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
      - SECURITY__JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SECURITY__SECRET_KEY=${SECRET_KEY}
      - LOGGING__LOG_LEVEL=INFO
      - LOGGING__STRUCTURED_LOGGING=true
      - LOGGING__JSON_LOGS=true
    depends_on:
      - postgres
      - mysql
      - redis
      - elasticsearch
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_static:/app/static
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=biohub_prod
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init-prod.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=biohub_prod
      - MYSQL_USER=biohub
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init-prod.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./docker/redis/redis-prod.conf:/usr/local/etc/redis/redis.conf
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms2g -Xmx2g
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=${ELASTICSEARCH_PASSWORD}
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
    healthcheck:
      test: ["CMD-SHELL", "curl -u elastic:${ELASTICSEARCH_PASSWORD} -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx-prod.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - app_static:/var/www/static
      - ./docker/ssl:/etc/ssl/certs
    depends_on:
      - app
    networks:
      - biohub-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.2'
          memory: 128M

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - biohub-network
    restart: unless-stopped

  # 日志聚合
  loki:
    image: grafana/loki:2.8.0
    ports:
      - "3100:3100"
    volumes:
      - ./docker/loki/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    networks:
      - biohub-network
    restart: unless-stopped

  # 可视化面板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
      - loki
    networks:
      - biohub-network
    restart: unless-stopped

volumes:
  postgres_data:
  mysql_data:
  redis_data:
  elasticsearch_data:
  app_uploads:
  app_logs:
  app_static:
  prometheus_data:
  loki_data:
  grafana_data:

networks:
  biohub-network:
    driver: bridge