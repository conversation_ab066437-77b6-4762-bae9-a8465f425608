import json  # 用于解析JSON响应
from functools import lru_cache  # 用于缓存函数结果
from textwrap import dedent  # 用于删除多行字符串中的公共前导空格
from typing import AsyncGenerator, List, Tuple  # 类型提示工具

from agno.agent import Agent  # 导入Agent类，用于创建能执行特定任务的智能代理
from agno.document.base import Document  # 文档基类，表示要处理的文本文档
from agno.document.chunking.strategy import ChunkingStrategy  # 分块策略基类
from agno.models.base import Model  # 模型基类，表示大语言模型接口
from agno.models.message import Message  # 消息类，用于与模型交互


class AgenticChunking(ChunkingStrategy):  # 继承自ChunkingStrategy基类
    """
    使用大语言模型(LLM)来确定文本中自然断点的分块策略
    Tips: 实验性代码，效果并不稳定
    """

    chunking_agent: Agent
    # 缓存大小设置，用于存储常见文本模式的断点决策
    CACHE_SIZE = 128

    # 常见的自然断点标记，用于启发式分块，按优先级排序
    NATURAL_BREAKPOINTS = [
        "\n\n\n",  # 多个空行（段落之间的明显分隔）
        "\n\n",  # 段落分隔
        ".\n\n",  # 句子结束并有段落分隔
        "?\n\n",  # 问句结束并有段落分隔
        "!\n\n",  # 感叹句结束并有段落分隔
        ".\n",  # 句子结束并换行
        "?\n",  # 问句结束并换行
        "!\n",  # 感叹句结束并换行
        ";\n",  # 分号并换行
        ":\n",  # 冒号并换行（如列表项开始）
        "。\n",  # 中文句号并换行
        "，\n",  # 中文逗号并换行
        "、\n",  # 中文顿号并换行
    ]

    # 最小块大小，小于此大小的文本不会被进一步分割
    MIN_CHUNK_SIZE = 100

    def __init__(self, model: Model, max_chunk_size: int = 1024, batch_size: int = 3):
        """初始化AgenticChunking分块策略

        这个方法创建一个基于大语言模型的智能分块策略，能够根据文本的语义结构
        自动确定最佳分块点，保证每个块都是语义完整的单元。

        参数:
            model: 用于确定文本断点的大语言模型实例，必须提供一个有效的模型
            max_chunk_size: 每个文本块的最大令牌数，默认为1024个令牌
        """
        # 参数验证：确保提供了有效的模型实例
        # 这是一个重要的前置检查，因为整个分块策略依赖于模型的能力
        if model is None:
            raise ValueError("必须提供一个有效的模型实例")

        # 保存配置参数到实例变量
        self.max_chunk_size = max_chunk_size  # 设置每个块的最大大小限制
        self.model = model  # 保存模型实例引用
        self.batch_size = batch_size  # 批处理大小，一次向LLM请求多少个断点
        self.breakpoint_cache = {}  # 简单的断点缓存，用于存储常见文本模式的断点位置

        # 创建一个专门用于文本分块的智能代理
        # 这个代理将负责分析文本并确定最佳的分割点
        self.chunking_agent = Agent(
            model=self.model,  # 使用传入的模型实例
            # 为代理设置角色描述，定义其专注于语义分割的能力
            description=dedent(
                """你是一个高效的文本处理代理，专门理解内容的语义和逻辑结构。你的唯一目的是将长输入文本智能地分割成**语义完整的单元**，优先考虑语义连贯性和完整性，而不是严格遵守最大长度限制。你的主要目标是确保每个片段保持其完整的上下文含义。"""
            ),
            # 为代理提供详细的指令，说明如何执行分块任务
            # 使用f-string将当前实例的max_chunk_size动态插入到指令中
            instructions=dedent(f"""
请按照以下优先级顺序精确处理输入文本：

1. **语义完整性（最高优先级）：**

   * 识别文本分割的逻辑和语义边界。理想的分割点包括段落间隔、句子结尾（句号、问号、感叹号）和明确的主题转换。
   * **绝不在单个句子、核心短语、完整思想或单词内部分割。** 每个输出单元必须在其原始上下文中保持语义完整且独立可理解。
   * **语义连贯性是你的首要目标** - 确保每个块代表一个有意义的完整语义单元。
   * 如果维持语义完整性需要超过长度限制，请优先考虑完整性而非长度。

2. **上下文保留：**

   * 每个块应保留理解其内容所需的必要上下文。
   * 避免创建需要其他块信息才能理解的块。
   * 当分割包含交叉引用或相互依赖信息的部分时，尽量保持相关内容在一起。

3. **长度指南（次于语义完整性）：**

   * 每个分割单元应尽量接近{self.max_chunk_size}个标记的长度，但这只是指导原则，而非严格限制。
   * 如果语义完整的片段超过{self.max_chunk_size}标记的指导原则，首先尝试找到保留意义的自然断点。
   * 只有当语义单元明显长于指导原则（例如，超过目标长度50%）且包含自然子划分时，才尝试在语义单元内寻找次要分割点。
   * **如果一个片段本质上不可分割而不破坏其含义，则无论长度如何都保持其完整性。**

4. **输入：**

   * 将直接提供要处理的文本。

5. **输出格式（关键 - 请仔细阅读）：**

   * 你的输出**必须是一个有效的JSON数组，包含分割后的文本块**。
   * 数组中的每个元素必须是一个字符串，代表一个语义完整的文本块。
   * 输出**必须以`[`开始，以`]`结束**，JSON数组前后不能有任何字符。
   * **不要将JSON包装在Markdown代码块中（例如，\`\`\`json\`或任何其他反引号）。**
   * **不要包含任何对话文本、解释、介绍性短语、结束语或JSON数组之外的任何内容。**
   * **严格遵循以下确切的JSON结构（不带任何周围的markdown）：**

    ```
    ["第一个文本块", "第二个文本块", "第三个文本块"]
    ```
    
   * 如果你被要求只找到一个文本块，你可以返回只包含一个元素的数组。
   * 如果你找不到合适的分割点来保持语义完整性，返回完整的语义单元，即使它超过了长度指南。
    """),
        )

    @lru_cache(maxsize=CACHE_SIZE)
    def _find_heuristic_breakpoint(self, text: str, max_size: int) -> int:
        """使用启发式规则查找文本的自然断点

        在不调用LLM的情况下，基于常见的文本结构特征查找合适的断点位置。
        优先考虑语义完整性，而非严格遵守最大大小限制。

        参数:
            text: 要查找断点的文本
            max_size: 推荐的块大小（非严格限制）

        返回:
            断点位置（字符索引）
        """
        # 如果文本长度小于等于推荐大小，直接返回全部文本
        if len(text) <= max_size:
            return len(text)

        # 首先尝试在推荐大小范围内查找自然断点
        text_to_check = text[:max_size]
        best_position = -1

        # 按优先级检查各种断点标记（NATURAL_BREAKPOINTS已按优先级排序）
        for marker in self.NATURAL_BREAKPOINTS:
            # 从后向前查找最后一个出现的标记
            pos = text_to_check.rfind(marker)
            if pos > 0:  # 找到了有效的断点
                # 加上标记长度，确保包含完整的标记
                return pos + len(marker)

        # 如果在推荐大小范围内没有找到标准断点，扩大搜索范围
        # 但限制在推荐大小的150%以内，以平衡语义完整性和长度
        extended_max = min(len(text), int(max_size * 1.5))
        if extended_max > max_size:
            text_to_check = text[:extended_max]

            # 在扩展范围内再次查找自然断点
            for marker in self.NATURAL_BREAKPOINTS:
                pos = text_to_check.rfind(marker)
                if pos > max_size and pos > best_position:  # 只考虑超过原推荐大小的断点
                    best_position = pos + len(marker)

            if best_position > 0:
                return best_position

        # 如果仍然没有找到标准断点，尝试在句子结束处断开
        # 查找最后一个句号、问号或感叹号后跟空格的位置
        for punct in [". ", "? ", "! ", "。 ", "？ ", "！ "]:
            pos = text_to_check.rfind(punct)
            if pos > 0 and pos > best_position:
                best_position = pos + len(punct)

        if best_position > 0:
            return best_position

        # 如果仍然没有找到合适的断点，尝试在最后一个空格处断开
        # 但要确保不会在单词中间断开
        pos = text_to_check.rfind(" ")
        if pos > 0:
            return pos + 1

        # 如果实在找不到任何语义上合理的断点，
        # 则返回推荐大小，但这应该是非常罕见的情况
        return max_size

    def _parse_llm_text_chunks(self, response_content: str) -> List[str]:
        """解析LLM返回的文本块

        尝试将LLM的响应解析为文本块列表。如果解析失败，返回空列表。

        参数:
            response_content: LLM的响应内容

        返回:
            文本块列表
        """
        try:
            # 尝试解析为JSON
            content = response_content.strip()

            # 处理JSON数组
            chunks = json.loads(content)

            # 确保所有元素都是字符串
            if isinstance(chunks, list):
                return [
                    chunk
                    for chunk in chunks
                    if isinstance(chunk, str) and chunk.strip()
                ]
            elif isinstance(chunks, str) and chunks.strip():
                return [chunks]

        except Exception:
            # 解析失败，返回空列表
            pass

        # 默认返回空列表
        return []

    def _get_text_chunks(self, text: str, count: int = 1) -> List[str]:
        """获取文本的分块结果

        首先尝试使用LLM直接分块，如果失败则回退到启发式规则。
        为避免因大模型输出长度限制导致文本丢失，每次只处理不超过8K tokens的文本。

        参数:
            text: 要分块的文本
            count: 建议的分块数量

        返回:
            文本块列表
        """
        # 如果文本较短，直接返回原文本
        if len(text) <= self.max_chunk_size:
            return [text]

        # 生成缓存键
        # 为了避免缓存过大的文本，只使用文本的前100个字符和后100个字符作为特征
        text_feature = text[:100] + text[-100:] if len(text) > 200 else text
        cache_key = f"{text_feature}_{count}_{self.max_chunk_size}"

        # 检查缓存
        if cache_key in self.breakpoint_cache:
            return self.breakpoint_cache[cache_key]

        # 估算文本长度是否超过8K tokens的限制
        # 一般来说，中文每个字符约为1.5个token，英文每个单词约为1.3个token
        # 这里使用保守估计，假设每个字符为1个token
        estimated_tokens = len(text)

        # 如果估计的token数超过8000，使用启发式方法先进行初步分割
        if estimated_tokens > 8000:
            # 使用启发式方法将文本分割成多个小于8000 tokens的片段
            result = []
            remaining = text
            while len(remaining) > 0:
                # 如果剩余文本小于8000 tokens，直接处理
                if len(remaining) <= 8000:
                    sub_chunks = self._process_text_chunk(remaining, max(1, count // 2))
                    result.extend(sub_chunks)
                    break

                # 否则，找到一个合适的断点，将文本分割成小于8000 tokens的片段
                breakpoint = self._find_heuristic_breakpoint(
                    remaining, 7000
                )  # 留出一些余量
                if breakpoint <= 0 or breakpoint >= len(remaining):
                    # 如果找不到合适的断点，强制在7000字符处截断
                    breakpoint = 7000

                # 处理当前片段
                current_chunk = remaining[:breakpoint].strip()
                sub_chunks = self._process_text_chunk(current_chunk, max(1, count // 2))
                result.extend(sub_chunks)

                # 更新剩余文本
                remaining = remaining[breakpoint:].strip()

            return result
        else:
            # 如果文本长度在8000 tokens以内，直接处理
            return self._process_text_chunk(text, count)

    def _process_text_chunk(self, text: str, count: int = 1) -> List[str]:
        """处理单个文本块，确保其不超过8K tokens

        参数:
            text: 要处理的文本块
            count: 建议的分块数量

        返回:
            处理后的文本块列表
        """
        try:
            # 调用LLM智能代理直接获取分块结果
            prompt = f"请将以下文本分割成{count}个语义完整的块。优先考虑语义完整性和连贯性，而不是严格遵守{self.max_chunk_size}个字符的长度指南。每个块必须是一个在其原始上下文中有意义的完整语义单元。将分块结果作为JSON字符串数组返回：\n\n{text}"

            response = self.chunking_agent.run(Message(role="user", content=prompt))

            if response and response.content:
                # 解析文本块
                chunks = self._parse_llm_text_chunks(response.content)

                # 如果成功解析到文本块，缓存并返回
                if chunks:
                    # 生成缓存键
                    text_feature = text[:100] + text[-100:] if len(text) > 200 else text
                    cache_key = f"{text_feature}_{count}_{self.max_chunk_size}"
                    self.breakpoint_cache[cache_key] = chunks
                    return chunks

        except Exception:
            # 异常处理：如果模型调用失败，回退到启发式规则
            pass

        # 如果LLM调用失败或返回的结果无效，使用启发式规则
        # 使用启发式断点分割文本
        breakpoint = self._find_heuristic_breakpoint(text, self.max_chunk_size)
        if breakpoint >= len(text):
            return [text]
        else:
            # 递归处理剩余文本
            first_chunk = text[:breakpoint].strip()
            remaining_text = text[breakpoint:].strip()
            result = [first_chunk]
            if remaining_text:
                result.extend(self._process_text_chunk(remaining_text, count - 1))
            return result

    def chunk(self, document: Document) -> List[Document]:
        """使用LLM将文本分割成多个语义连贯的块

        基于上下文确定自然断点，使分块更符合语义完整性。这种方法比简单的按字符数或句子
        分割更智能，能够保持文本的语义完整性，使得每个块都是有意义的独立单元。

        参数:
            document: 需要分块的文档对象，包含要处理的文本内容和元数据

        返回:
            分块后的文档对象列表，每个文档对象包含一个语义完整的文本块
        """
        # 优化处理：如果文档内容小于等于最大块大小，则无需分块，直接返回原文档
        if len(document.content) <= self.max_chunk_size:
            return [document]

        # 初始化分块结果列表，用于存储所有生成的文本块
        chunks: List[Document] = []
        # 清理文本内容，移除不必要的空白字符等
        cleaned_text = self.clean_text(document.content)
        # 复制文档元数据，确保每个分块都保留原始文档的元信息
        chunk_meta_data = document.meta_data

        # 确定要处理的批次大小
        batch_count = min(
            self.batch_size, max(1, len(cleaned_text) // self.max_chunk_size)
        )

        # 直接获取文本块
        text_chunks = self._get_text_chunks(cleaned_text, batch_count)

        # 处理每个文本块
        for chunk_number, chunk_content in enumerate(text_chunks, 1):
            # 如果块为空，跳过
            if not chunk_content:
                continue

            # 为每个块创建独立的元数据副本，并添加块特有的信息
            meta_data = chunk_meta_data.copy()
            meta_data["chunk"] = chunk_number

            # 为每个块生成唯一标识符
            chunk_id = None
            if document.id:
                chunk_id = f"{document.id}_{chunk_number}"
            elif document.name:
                chunk_id = f"{document.name}_{chunk_number}"

            # 记录块的大小（字符数）到元数据
            meta_data["chunk_size"] = len(chunk_content)

            # 创建新的文档对象表示当前块，并添加到结果列表
            chunks.append(
                Document(
                    id=chunk_id,
                    name=document.name,
                    meta_data=meta_data,
                    content=chunk_content,
                )
            )

        # 返回所有生成的文档块列表
        # 这些块共同组成了原始文档的完整内容，但每个块都是语义完整的单元
        return chunks

    async def async_chunk(
        self, document: Document
    ) -> AsyncGenerator[Tuple[Document, float], None]:
        """异步分块方法，在每次完成一个分块后yield分块内容和当前进度

        这个方法是chunk方法的异步版本，它在处理每个分块后会立即yield结果，
        而不是等待所有分块完成后一次性返回。这对于处理大型文档特别有用，
        可以在分块过程中实时获取进度和结果。

        参数:
            document: 需要分块的文档对象，包含要处理的文本内容和元数据

        yields:
            元组(分块文档, 当前进度百分比)，其中进度范围为0.0到1.0
        """
        # 优化处理：如果文档内容小于等于最大块大小，则无需分块，直接yield原文档和100%进度
        if len(document.content) <= self.max_chunk_size:
            yield document, 1.0
            return

        # 清理文本内容，移除不必要的空白字符等
        original_text = document.content
        cleaned_text = self.clean_text(original_text)
        original_length = len(original_text)  # 用于计算进度
        processed_length = 0  # 已处理的文本长度

        # 复制文档元数据，确保每个分块都保留原始文档的元信息
        chunk_meta_data = document.meta_data

        # 确定要处理的批次大小
        batch_count = min(
            self.batch_size, max(1, len(cleaned_text) // self.max_chunk_size)
        )

        # 直接获取文本块
        text_chunks = self._get_text_chunks(cleaned_text, batch_count)

        # 处理每个文本块
        for chunk_number, chunk_content in enumerate(text_chunks, 1):
            # 如果块为空，跳过
            if not chunk_content:
                continue

            chunk_length = len(chunk_content)
            processed_length += chunk_length

            # 为每个块创建独立的元数据副本，并添加块特有的信息
            meta_data = chunk_meta_data.copy()
            meta_data["chunk"] = chunk_number

            # 为每个块生成唯一标识符
            chunk_id = None
            if document.id:
                chunk_id = f"{document.id}_{chunk_number}"
            elif document.name:
                chunk_id = f"{document.name}_{chunk_number}"

            # 记录块的大小（字符数）到元数据
            meta_data["chunk_size"] = chunk_length

            # 创建新的文档对象表示当前块
            chunk_doc = Document(
                id=chunk_id,
                name=document.name,
                meta_data=meta_data,
                content=chunk_content,
            )

            # 计算当前进度（0.0到1.0之间）
            progress = min(processed_length / original_length, 1.0)

            # yield当前分块和进度
            yield chunk_doc, progress
