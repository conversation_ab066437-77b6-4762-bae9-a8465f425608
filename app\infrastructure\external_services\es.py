from elasticsearch import Elasticsearch
from elasticsearch_dsl import (
    DenseVector,
    Document,
    Integer,
    Keyword,
    Text,
    connections,
)

from app.core.config.config import settings

connections.create_connection(
    hosts=settings.ELASTICSEARCH_URL, api_key=settings.ELASTICSEARCH_API_KEY
)


def get_es_client() -> Elasticsearch:
    """获取Elasticsearch客户端"""
    return Elasticsearch(
        hosts=settings.ELASTICSEARCH_URL, api_key=settings.ELASTICSEARCH_API_KEY
    )


class DocumentChunkES(Document):
    """Elasticsearch文档片段模型"""

    knowledge_base_id = Integer()
    document_id = Integer()
    chunk_id = Integer()
    title = Text(analyzer="ik_max_word", search_analyzer="ik_smart")  # 中文分词
    content = Text(analyzer="ik_max_word", search_analyzer="ik_smart")
    embedding = DenseVector(dims=1024)  # 向量字段
    metadata = Keyword()
    created_at = Keyword()

    class Index:
        name = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_document_chunks"
        settings = {"number_of_shards": 1, "number_of_replicas": 0}


DocumentChunkES.init()


class DocumentChunkService:
    """文档块服务，用于管理Elasticsearch中的文档块"""

    def __init__(self):
        self.es = get_es_client()
        self.index_name = DocumentChunkES._index._name

    async def index_document_chunk(self, chunk_data: dict):
        """索引单个文档块"""
        doc = DocumentChunkES(**chunk_data)
        doc.save()

    async def delete_document_chunks(self, document_id: int):
        """根据document_id删除文档块"""
        query = {"query": {"term": {"document_id": document_id}}}
        self.es.delete_by_query(index=self.index_name, body=query)

    async def search_by_vector(self, query_vector, knowledge_base_id=None, top_k=5):
        """向量搜索

        参数:
            query_vector: 查询向量
            knowledge_base_id: 知识库ID，可选
            top_k: 返回结果数量

        返回:
            搜索结果列表
        """
        script_query = {
            "script_score": {
                "query": {"bool": {}},
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": query_vector},
                },
            }
        }

        # 如果指定了知识库ID，添加过滤条件
        if knowledge_base_id is not None:
            script_query["script_score"]["query"]["bool"]["filter"] = [
                {"term": {"knowledge_base_id": knowledge_base_id}}
            ]

        search_query = {
            "size": top_k,
            "query": script_query,
            "_source": {
                "includes": ["document_id", "chunk_id", "title", "content", "metadata"]
            },
        }

        response = self.es.search(index=self.index_name, body=search_query)
        return [hit["_source"] for hit in response["hits"]["hits"]]

    async def search_by_keyword(self, keyword, knowledge_base_id=None, top_k=5):
        """关键字搜索

        参数:
            keyword: 关键字
            knowledge_base_id: 知识库ID，可选
            top_k: 返回结果数量

        返回:
            搜索结果列表
        """
        bool_query = {
            "bool": {
                "should": [
                    {"match": {"title": {"query": keyword, "boost": 2}}},
                    {"match": {"content": {"query": keyword}}},
                ],
                "minimum_should_match": 1,
            }
        }

        # 如果指定了知识库ID，添加过滤条件
        if knowledge_base_id is not None:
            bool_query["bool"]["filter"] = [
                {"term": {"knowledge_base_id": knowledge_base_id}}
            ]

        search_query = {
            "size": top_k,
            "query": bool_query,
            "_source": {
                "includes": ["document_id", "chunk_id", "title", "content", "metadata"]
            },
        }

        response = self.es.search(index=self.index_name, body=search_query)
        return [hit["_source"] for hit in response["hits"]["hits"]]
