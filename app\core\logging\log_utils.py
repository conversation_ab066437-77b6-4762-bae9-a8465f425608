import time
from functools import wraps
from typing import Any, Callable, Dict

from app.core.logging.log_config import get_logger


def log_execution_time(logger_name: str = "app", log_level: str = "INFO"):
    """记录函数执行时间的装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = time.time()
            func_name = f"{func.__module__}.{func.__name__}"

            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time

                log_message = f"函数 {func_name} 执行完成，耗时: {execution_time:.3f}秒"
                getattr(logger, log_level.lower())(log_message)

                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"函数 {func_name} 执行失败，耗时: {execution_time:.3f}秒，错误: {str(e)}",
                    exc_info=True,
                )
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = time.time()
            func_name = f"{func.__module__}.{func.__name__}"

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                log_message = f"函数 {func_name} 执行完成，耗时: {execution_time:.3f}秒"
                getattr(logger, log_level.lower())(log_message)

                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"函数 {func_name} 执行失败，耗时: {execution_time:.3f}秒，错误: {str(e)}",
                    exc_info=True,
                )
                raise

        # 根据函数类型返回对应的包装器
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def log_api_call(logger_name: str = "app.api"):
    """记录API调用的装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            func_name = f"{func.__module__}.{func.__name__}"

            # 记录API调用开始
            logger.info(f"API调用开始: {func_name}")

            try:
                result = await func(*args, **kwargs)
                logger.info(f"API调用成功: {func_name}")
                return result
            except Exception as e:
                logger.error(f"API调用失败: {func_name}，错误: {str(e)}", exc_info=True)
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            func_name = f"{func.__module__}.{func.__name__}"

            # 记录API调用开始
            logger.info(f"API调用开始: {func_name}")

            try:
                result = func(*args, **kwargs)
                logger.info(f"API调用成功: {func_name}")
                return result
            except Exception as e:
                logger.error(f"API调用失败: {func_name}，错误: {str(e)}", exc_info=True)
                raise

        # 根据函数类型返回对应的包装器
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def log_database_operation(operation_type: str, logger_name: str = "app.database"):
    """记录数据库操作的装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time

                logger.info(
                    f"数据库{operation_type}操作成功，耗时: {execution_time:.3f}秒",
                    extra={"operation_type": operation_type},
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"数据库{operation_type}操作失败，耗时: {execution_time:.3f}秒，错误: {str(e)}",
                    extra={"operation_type": operation_type},
                    exc_info=True,
                )
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                logger.info(
                    f"数据库{operation_type}操作成功，耗时: {execution_time:.3f}秒",
                    extra={"operation_type": operation_type},
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"数据库{operation_type}操作失败，耗时: {execution_time:.3f}秒，错误: {str(e)}",
                    extra={"operation_type": operation_type},
                    exc_info=True,
                )
                raise

        # 根据函数类型返回对应的包装器
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


class StructuredLogger:
    """结构化日志记录器"""

    def __init__(self, logger_name: str = "app"):
        self.logger = get_logger(logger_name)

    def log_user_action(
        self,
        user_id: str,
        action: str,
        resource: str = None,
        details: Dict[str, Any] = None,
        request_id: str = None,
    ):
        """记录用户操作日志"""
        extra = {
            "user_id": user_id,
            "action": action,
            "resource": resource,
            "details": details or {},
            "request_id": request_id or "unknown",
        }

        self.logger.info(f"用户操作: {action}", extra=extra)

    def log_system_event(
        self,
        event_type: str,
        message: str,
        details: Dict[str, Any] = None,
        level: str = "INFO",
    ):
        """记录系统事件日志"""
        extra = {"event_type": event_type, "details": details or {}}

        getattr(self.logger, level.lower())(f"系统事件: {message}", extra=extra)

    def log_security_event(
        self,
        event_type: str,
        user_id: str = None,
        ip_address: str = None,
        details: Dict[str, Any] = None,
    ):
        """记录安全事件日志"""
        extra = {
            "event_type": "security",
            "security_event_type": event_type,
            "user_id": user_id,
            "ip_address": ip_address,
            "details": details or {},
        }

        self.logger.warning(f"安全事件: {event_type}", extra=extra)

    def log_performance_metric(
        self,
        metric_name: str,
        value: float,
        unit: str = "ms",
        details: Dict[str, Any] = None,
    ):
        """记录性能指标日志"""
        extra = {
            "metric_type": "performance",
            "metric_name": metric_name,
            "value": value,
            "unit": unit,
            "details": details or {},
        }

        self.logger.info(f"性能指标: {metric_name}={value}{unit}", extra=extra)


# 全局结构化日志记录器实例
structured_logger = StructuredLogger()
