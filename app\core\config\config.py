from app.core.config.settings import settings

# 向后兼容的导出
__all__ = ["settings"]

# 保持向后兼容性的属性访问
class LegacySettings:
    """向后兼容的设置类"""
    
    def __init__(self, settings_instance):
        self._settings = settings_instance
    
    @property
    def DEBUG(self):
        return self._settings.app.DEBUG
    
    @property
    def HOST(self):
        return self._settings.app.HOST
    
    @property
    def PORT(self):
        return self._settings.app.PORT
    
    @property
    def WORKERS(self):
        return self._settings.app.WORKERS
    
    @property
    def API_V1_STR(self):
        return self._settings.app.API_V1_STR
    
    @property
    def CORS_ORIGINS(self):
        return self._settings.app.CORS_ORIGINS
    
    @property
    def AUTH_SERVICE_URL(self):
        return self._settings.security.AUTH_SERVICE_URL
    
    @property
    def POSTGRES_SERVER(self):
        return self._settings.database.POSTGRES_SERVER
    
    @property
    def POSTGRES_USER(self):
        return self._settings.database.POSTGRES_USER
    
    @property
    def POSTGRES_PASSWORD(self):
        return self._settings.database.POSTGRES_PASSWORD
    
    @property
    def POSTGRES_DB(self):
        return self._settings.database.POSTGRES_DB
    
    @property
    def POSTGRES_PORT(self):
        return self._settings.database.POSTGRES_PORT
    
    @property
    def DATABASE_URL(self):
        return self._settings.database.postgres_url
    
    @property
    def MYSQL_SERVER(self):
        return self._settings.database.MYSQL_SERVER
    
    @property
    def MYSQL_USER(self):
        return self._settings.database.MYSQL_USER
    
    @property
    def MYSQL_PASSWORD(self):
        return self._settings.database.MYSQL_PASSWORD
    
    @property
    def MYSQL_DB(self):
        return self._settings.database.MYSQL_DB
    
    @property
    def MYSQL_PORT(self):
        return self._settings.database.MYSQL_PORT
    
    @property
    def MYSQL_DATABASE_URL(self):
        return self._settings.database.mysql_url
    
    @property
    def REDIS_URL(self):
        return self._settings.REDIS_URL
    
    @property
    def REDIS_HOST(self):
        return self._settings.REDIS_HOST
    
    @property
    def REDIS_PORT(self):
        return self._settings.REDIS_PORT
    
    @property
    def REDIS_DB(self):
        return self._settings.REDIS_DB
    
    @property
    def REDIS_PASSWORD(self):
        return self._settings.REDIS_PASSWORD
    
    @property
    def ELASTICSEARCH_INDEX_PREFIX(self):
        return self._settings.ELASTICSEARCH_INDEX_PREFIX
    
    @property
    def ELASTICSEARCH_API_KEY(self):
        return self._settings.ELASTICSEARCH_API_KEY
    
    @property
    def ELASTICSEARCH_URL(self):
        return self._settings.ELASTICSEARCH_URL
    
    @property
    def DASHSCOPE_API_KEY(self):
        return self._settings.DASHSCOPE_API_KEY
    
    @property
    def UPLOAD_DIR(self):
        return self._settings.UPLOAD_DIR
    
    @property
    def LOG_LEVEL(self):
        return self._settings.logging.LOG_LEVEL
    
    @property
    def LOG_FILE(self):
        return self._settings.logging.LOG_FILE

# 创建兼容性实例
legacy_settings = LegacySettings(settings)

# 默认导出（向后兼容）
settings = legacy_settings
