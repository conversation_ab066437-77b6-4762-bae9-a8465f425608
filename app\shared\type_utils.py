"""
类型检查和验证工具
提供运行时类型检查和验证功能
"""

import inspect
from typing import (
    Any, Dict, List, Optional, Union, Callable, TypeVar, Generic, get_type_hints,
    get_origin, get_args, Type
)
from functools import wraps
from dataclasses import dataclass, field

from app.core.exceptions import ValidationException


T = TypeVar('T')


@dataclass
class TypeCheckResult:
    """类型检查结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class TypeValidator:
    """类型验证器"""
    
    @staticmethod
    def validate_type(value: Any, expected_type: Type) -> TypeCheckResult:
        """
        验证值是否符合指定类型
        
        Args:
            value: 要验证的值
            expected_type: 期望的类型
        
        Returns:
            TypeCheckResult: 验证结果
        
        Examples:
            >>> validator = TypeValidator()
            >>> result = validator.validate_type(123, int)
            >>> print(result.is_valid)
            True
            >>> result = validator.validate_type("hello", int)
            >>> print(result.is_valid)
            False
        """
        result = TypeCheckResult(is_valid=True)
        
        try:
            # 获取类型的origin和args
            origin = get_origin(expected_type)
            args = get_args(expected_type)
            
            if origin is None:
                # 简单类型
                if not isinstance(value, expected_type):
                    result.is_valid = False
                    result.errors.append(f"Expected {expected_type.__name__}, got {type(value).__name__}")
            
            elif origin is Union:
                # Union类型（包括Optional）
                if not any(TypeValidator.validate_type(value, arg).is_valid for arg in args):
                    result.is_valid = False
                    type_names = [arg.__name__ if hasattr(arg, '__name__') else str(arg) for arg in args]
                    result.errors.append(f"Expected one of {type_names}, got {type(value).__name__}")
            
            elif origin is list:
                # List类型
                if not isinstance(value, list):
                    result.is_valid = False
                    result.errors.append(f"Expected list, got {type(value).__name__}")
                elif args:
                    # 检查列表元素类型
                    item_type = args[0]
                    for i, item in enumerate(value):
                        item_result = TypeValidator.validate_type(item, item_type)
                        if not item_result.is_valid:
                            result.is_valid = False
                            result.errors.append(f"Item {i}: {item_result.errors[0]}")
            
            elif origin is dict:
                # Dict类型
                if not isinstance(value, dict):
                    result.is_valid = False
                    result.errors.append(f"Expected dict, got {type(value).__name__}")
                elif len(args) == 2:
                    # 检查字典键值类型
                    key_type, value_type = args
                    for k, v in value.items():
                        key_result = TypeValidator.validate_type(k, key_type)
                        if not key_result.is_valid:
                            result.is_valid = False
                            result.errors.append(f"Key {k}: {key_result.errors[0]}")
                        
                        value_result = TypeValidator.validate_type(v, value_type)
                        if not value_result.is_valid:
                            result.is_valid = False
                            result.errors.append(f"Value for key {k}: {value_result.errors[0]}")
            
            else:
                # 其他泛型类型
                if not isinstance(value, origin):
                    result.is_valid = False
                    result.errors.append(f"Expected {origin.__name__}, got {type(value).__name__}")
        
        except Exception as e:
            result.is_valid = False
            result.errors.append(f"Type validation error: {str(e)}")
        
        return result
    
    @staticmethod
    def validate_function_args(func: Callable, *args, **kwargs) -> TypeCheckResult:
        """
        验证函数参数类型
        
        Args:
            func: 要验证的函数
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            TypeCheckResult: 验证结果
        
        Examples:
            >>> def example_func(a: int, b: str) -> bool:
            ...     return True
            >>> result = TypeValidator.validate_function_args(example_func, 1, "hello")
            >>> print(result.is_valid)
            True
        """
        result = TypeCheckResult(is_valid=True)
        
        try:
            # 获取函数签名和类型注解
            sig = inspect.signature(func)
            type_hints = get_type_hints(func)
            
            # 绑定参数
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证每个参数
            for param_name, param_value in bound_args.arguments.items():
                if param_name in type_hints:
                    expected_type = type_hints[param_name]
                    param_result = TypeValidator.validate_type(param_value, expected_type)
                    if not param_result.is_valid:
                        result.is_valid = False
                        for error in param_result.errors:
                            result.errors.append(f"Parameter '{param_name}': {error}")
        
        except Exception as e:
            result.is_valid = False
            result.errors.append(f"Function argument validation error: {str(e)}")
        
        return result
    
    @staticmethod
    def validate_return_type(func: Callable, return_value: Any) -> TypeCheckResult:
        """
        验证函数返回值类型
        
        Args:
            func: 函数对象
            return_value: 返回值
        
        Returns:
            TypeCheckResult: 验证结果
        
        Examples:
            >>> def example_func() -> int:
            ...     return 42
            >>> result = TypeValidator.validate_return_type(example_func, 42)
            >>> print(result.is_valid)
            True
        """
        result = TypeCheckResult(is_valid=True)
        
        try:
            type_hints = get_type_hints(func)
            if 'return' in type_hints:
                expected_type = type_hints['return']
                return_result = TypeValidator.validate_type(return_value, expected_type)
                if not return_result.is_valid:
                    result.is_valid = False
                    result.errors.extend(return_result.errors)
        
        except Exception as e:
            result.is_valid = False
            result.errors.append(f"Return type validation error: {str(e)}")
        
        return result


def type_checked(func: Callable) -> Callable:
    """
    类型检查装饰器，运行时验证函数参数和返回值类型
    
    Args:
        func: 要装饰的函数
    
    Returns:
        Callable: 装饰后的函数
    
    Examples:
        >>> @type_checked
        ... def add_numbers(a: int, b: int) -> int:
        ...     return a + b
        >>> add_numbers(1, 2)  # 正常执行
        3
        >>> add_numbers("1", "2")  # 抛出ValidationException
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 验证参数类型
        arg_result = TypeValidator.validate_function_args(func, *args, **kwargs)
        if not arg_result.is_valid:
            raise ValidationException(
                message=f"Type check failed for function {func.__name__}",
                field_errors={"arguments": arg_result.errors}
            )
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 验证返回值类型
        return_result = TypeValidator.validate_return_type(func, result)
        if not return_result.is_valid:
            raise ValidationException(
                message=f"Return type check failed for function {func.__name__}",
                field_errors={"return_value": return_result.errors}
            )
        
        return result
    
    return wrapper


def validate_schema(schema_class: Type[T]) -> Callable:
    """
    模式验证装饰器，验证函数参数是否符合指定的模式类
    
    Args:
        schema_class: 模式类
    
    Returns:
        Callable: 装饰器函数
    
    Examples:
        >>> from dataclasses import dataclass
        >>> @dataclass
        ... class UserSchema:
        ...     name: str
        ...     age: int
        >>> 
        >>> @validate_schema(UserSchema)
        ... def create_user(user_data: dict) -> UserSchema:
        ...     return UserSchema(**user_data)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数签名
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证每个参数
            for param_name, param_value in bound_args.arguments.items():
                param = sig.parameters[param_name]
                if param.annotation == dict:
                    # 尝试创建模式实例进行验证
                    try:
                        schema_class(**param_value)
                    except Exception as e:
                        raise ValidationException(
                            message=f"Schema validation failed for parameter {param_name}",
                            field_errors={param_name: [str(e)]}
                        )
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


class TypeConverter:
    """类型转换器"""
    
    @staticmethod
    def safe_convert(value: Any, target_type: Type[T]) -> Optional[T]:
        """
        安全类型转换
        
        Args:
            value: 要转换的值
            target_type: 目标类型
        
        Returns:
            Optional[T]: 转换后的值，转换失败返回None
        
        Examples:
            >>> converter = TypeConverter()
            >>> result = converter.safe_convert("123", int)
            >>> print(result)
            123
            >>> result = converter.safe_convert("abc", int)
            >>> print(result)
            None
        """
        try:
            if target_type == int:
                return int(value)
            elif target_type == float:
                return float(value)
            elif target_type == str:
                return str(value)
            elif target_type == bool:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif target_type == list:
                if isinstance(value, str):
                    # 尝试解析JSON
                    import json
                    return json.loads(value)
                return list(value)
            elif target_type == dict:
                if isinstance(value, str):
                    # 尝试解析JSON
                    import json
                    return json.loads(value)
                return dict(value)
            else:
                return target_type(value)
        except (ValueError, TypeError, AttributeError):
            return None
    
    @staticmethod
    def convert_with_validation(value: Any, target_type: Type[T]) -> T:
        """
        带验证的类型转换
        
        Args:
            value: 要转换的值
            target_type: 目标类型
        
        Returns:
            T: 转换后的值
        
        Raises:
            ValidationException: 转换失败时抛出
        
        Examples:
            >>> converter = TypeConverter()
            >>> result = converter.convert_with_validation("123", int)
            >>> print(result)
            123
        """
        result = TypeConverter.safe_convert(value, target_type)
        if result is None:
            raise ValidationException(
                message=f"Cannot convert {type(value).__name__} to {target_type.__name__}",
                field_errors={"value": [f"Invalid value: {value}"]}
            )
        return result


def auto_convert_types(func: Callable) -> Callable:
    """
    自动类型转换装饰器
    
    Args:
        func: 要装饰的函数
    
    Returns:
        Callable: 装饰后的函数
    
    Examples:
        >>> @auto_convert_types
        ... def add_numbers(a: int, b: int) -> int:
        ...     return a + b
        >>> result = add_numbers("1", "2")  # 自动转换为int
        >>> print(result)
        3
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 获取函数签名和类型注解
        sig = inspect.signature(func)
        type_hints = get_type_hints(func)
        
        # 绑定参数
        bound_args = sig.bind(*args, **kwargs)
        bound_args.apply_defaults()
        
        # 转换参数类型
        converted_args = {}
        for param_name, param_value in bound_args.arguments.items():
            if param_name in type_hints:
                expected_type = type_hints[param_name]
                # 如果类型不匹配，尝试转换
                if not isinstance(param_value, expected_type):
                    converted_value = TypeConverter.safe_convert(param_value, expected_type)
                    if converted_value is not None:
                        converted_args[param_name] = converted_value
                    else:
                        converted_args[param_name] = param_value
                else:
                    converted_args[param_name] = param_value
            else:
                converted_args[param_name] = param_value
        
        # 使用转换后的参数调用函数
        return func(**converted_args)
    
    return wrapper


# 导出所有类型检查工具
__all__ = [
    'TypeCheckResult',
    'TypeValidator',
    'TypeConverter',
    'type_checked',
    'validate_schema',
    'auto_convert_types',
]