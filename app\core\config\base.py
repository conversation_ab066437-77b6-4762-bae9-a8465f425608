from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pydantic import BaseModel, field_validator, ConfigDict


class BaseConfig(BaseModel, ABC):
    """配置基类"""
    
    model_config = ConfigDict(
        extra='ignore',
        case_sensitive=True,
        env_file=".env",
        env_file_encoding="utf-8"
    )
        
    @abstractmethod
    def validate_config(self) -> None:
        """验证配置"""
        pass
    
    @abstractmethod
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息（不包含敏感信息）"""
        pass