try:
	from main import app
	print('✅ Main application import successful')
	print('✅ FastAPI app created successfully')
	print(f'📝 App title: {app.title}')
	print(f'🔧 App version: {app.version}')
	print('🎉 All imports working correctly!')
	print('🚀 The application is ready to start with uvicorn!')
	print('')
	print('To start the server, run:')
	print(' uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000')
except Exception as e: 
  print(f'❌ Import error: {e}')
  import traceback
  traceback.print_exc()
