-- MySQL初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS biohub_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'biohub'@'%' IDENTIFIED BY 'biohub';

-- 授予权限
GRANT ALL PRIVILEGES ON biohub_dev.* TO 'biohub'@'%';
GRANT ALL PRIVILEGES ON biohub_dev.* TO 'root'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用数据库
USE biohub_dev;

-- 设置时区
SET time_zone = '+00:00';

-- 创建示例表（可选）
-- CREATE TABLE IF NOT EXISTS test_table (
--     id INT AUTO_INCREMENT PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );