from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, TypeVar, Generic, Callable

T = TypeVar("T")


class Container:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable[[], Any]] = {}
        self._singletons: Dict[str, Any] = {}
        self._bindings: Dict[str, str] = {}
    
    def bind(self, interface: str, implementation: Any) -> None:
        """绑定接口到实现"""
        self._bindings[interface] = implementation
    
    def singleton(self, key: str, factory: Callable[[], Any]) -> None:
        """注册单例服务"""
        self._factories[key] = factory
    
    def factory(self, key: str, factory: Callable[[], Any]) -> None:
        """注册工厂服务"""
        self._services[key] = factory
    
    def instance(self, key: str, instance: Any) -> None:
        """注册实例"""
        self._singletons[key] = instance
    
    def get(self, key: str) -> Any:
        """获取服务"""
        # 检查是否为单例
        if key in self._singletons:
            return self._singletons[key]
        
        # 检查是否为单例工厂
        if key in self._factories:
            if key not in self._singletons:
                self._singletons[key] = self._factories[key]()
            return self._singletons[key]
        
        # 检查是否为工厂
        if key in self._services:
            return self._services[key]()
        
        # 检查是否为绑定
        if key in self._bindings:
            return self.get(self._bindings[key])
        
        raise KeyError(f"Service '{key}' not found in container")
    
    def has(self, key: str) -> bool:
        """检查服务是否存在"""
        return (key in self._services or 
                key in self._factories or 
                key in self._singletons or 
                key in self._bindings)
    
    def clear(self) -> None:
        """清空容器"""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._bindings.clear()


class ServiceProvider(ABC):
    """服务提供者基类"""
    
    @abstractmethod
    def register(self, container: Container) -> None:
        """注册服务"""
        pass


class Injectable(ABC):
    """可注入的服务基类"""
    
    @abstractmethod
    def __init__(self, container: Container):
        """初始化服务"""
        pass


# 全局容器实例
container = Container()