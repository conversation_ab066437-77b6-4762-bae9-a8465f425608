import asyncio
import datetime
from typing import Any

from fastapi import APIRouter, Depends, Query, Request
from sse_starlette.sse import EventSourceResponse

from app.api import deps
from app.core.exceptions import (
    AuthorizationException,
    BusinessException,
    ResourceNotFoundException,
    ValidationException,
)
from app.shared.utils.response_utils import (
    created_response,
    no_content_response,
    paginated_response,
    success_response,
)

router = APIRouter()


@router.get("/success-example")
async def success_example() -> Any:
    """成功响应示例"""
    data = {"id": 1, "name": "示例数据", "description": "这是一个成功响应的示例"}

    return success_response(data=data, msg="获取数据成功")


@router.post("/create-example")
async def create_example() -> Any:
    """创建响应示例"""
    created_data = {
        "id": 2,
        "name": "新创建的数据",
        "created_at": "2024-01-01T00:00:00Z",
    }

    return created_response(data=created_data, msg="数据创建成功")


@router.get("/paginated-example")
async def paginated_example(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小"),
) -> Any:
    """分页响应示例"""
    # 模拟数据
    total = 100
    items = [
        {"id": i, "name": f"项目 {i}"}
        for i in range((page - 1) * page_size + 1, min(page * page_size + 1, total + 1))
    ]

    return paginated_response(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        msg="获取分页数据成功",
    )


@router.delete("/delete-example/{item_id}")
async def delete_example(item_id: int) -> Any:
    """删除响应示例"""
    if item_id <= 0:
        raise ValidationException("ID必须大于0")

    return no_content_response("删除成功")


@router.get("/not-found-example/{item_id}")
async def not_found_example(item_id: int) -> Any:
    """资源未找到异常示例"""
    if item_id == 404:
        raise ResourceNotFoundException("指定的资源不存在")

    return success_response(
        data={"id": item_id, "name": f"项目 {item_id}"}, msg="获取资源成功"
    )


@router.get("/forbidden-example")
async def forbidden_example(
    current_user_id: str = Depends(deps.get_current_user_id),
) -> Any:
    """权限不足异常示例"""
    # 模拟权限检查
    if current_user_id != "admin":
        raise AuthorizationException("只有管理员可以访问此资源")

    return success_response(
        data={"message": "管理员专用数据"}, msg="获取管理员数据成功"
    )


@router.post("/business-error-example")
async def business_error_example(amount: float = Query(..., description="金额")) -> Any:
    """业务逻辑异常示例"""
    if amount < 0:
        raise BusinessException(msg="金额不能为负数", error_code="INVALID_AMOUNT")

    if amount > 10000:
        raise BusinessException(msg="金额超过限制", error_code="AMOUNT_EXCEEDED")

    return success_response(
        data={"amount": amount, "status": "processed"}, msg="金额处理成功"
    )


@router.get("/general-error-example")
async def general_error_example() -> Any:
    """通用异常示例 - 这会触发未处理异常"""
    # 故意触发一个未处理的异常
    raise Exception("这是一个未处理的异常示例")


@router.get("/stream-example")
async def message_stream(request: Request):
    async def event_generator():
        while True:
            # 如果客户端断开连接，停止生成事件
            if await request.is_disconnected():
                break

            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message = f"data: Current time is {current_time}\n\n"
            yield message

            await asyncio.sleep(1)  # 每秒发送一次事件

    return EventSourceResponse(event_generator())
