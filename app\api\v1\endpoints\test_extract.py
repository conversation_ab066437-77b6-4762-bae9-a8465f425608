import os
import tempfile
from textwrap import dedent
from typing import Any

from agno.agent import Agent
from fastapi import APIRouter, File, UploadFile

from app.ai.models.dashscope import BailianLL<PERSON>
from app.core.config import settings
from app.core.exceptions import BusinessException  # 使用项目已有的异常类
from app.shared.utils.response_utils import success_response  # 使用项目已有的响应工具
from app.shared.utils.docx_tools import docx_to_html

# 占位符，用户需要根据实际情况修改
TEST_EXTRACTOR_DESCRIPTION = """
This is a test extractor agent.
"""
TEST_EXTRACTOR_INSTRUCTIONS = """
Extract information from the provided text based on the user's query.
Return the extracted information in JSON format.
"""

router = APIRouter(
    prefix="/test-extractor",
    tags=["test-extractor"],
    responses={404: {"description": "Not found"}},
)


@router.post("/extract")
async def extract(file: UploadFile = File(...)) -> Any:
    """
    从上传的 DOCX 文件中提取信息。

    Args:
        file: 上传的 DOCX 文件。

    Returns:
        提取到的信息，JSON 格式。

    Raises:
        BusinessException: 如果文件内容为空或处理失败。
        HTTPException: 如果模型调用失败。
    """
    # 创建临时文件来存储上传的文件内容
    with tempfile.NamedTemporaryFile(delete=False, suffix=".docx") as temp_file:
        try:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        except Exception as e:
            raise BusinessException(f"Failed to read or write uploaded file: {str(e)}")
        finally:
            await file.close()

    try:
        # 将 DOCX 转换为 HTML 代码
        docx_content = await docx_to_html(temp_file_path)
    except Exception as e:
        os.unlink(temp_file_path)  # 确保在转换失败时也删除临时文件
        raise BusinessException(f"Failed to convert DOCX to Markdown: {str(e)}")
    finally:
        # 确保临时文件被删除
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

    if not docx_content or docx_content.strip() == "":
        raise BusinessException("文件内容为空或转换后内容为空")

    try:
        extractor_agent = Agent(
            model=BailianLLM(
                id="qwen-turbo-latest",
                api_key=settings.DASHSCOPE_API_KEY,
            ),
            description=dedent(TEST_EXTRACTOR_DESCRIPTION),
            instructions=dedent(TEST_EXTRACTOR_INSTRUCTIONS),
        )

        agent_response = extractor_agent.run(
            {"role": "user", "content": docx_content},
        )
        return success_response(data=agent_response.content, msg="提取试题信息成功")
    except Exception as e:
        raise BusinessException(f"Fail to extract test by Agent: {str(e)}")
