from typing import Optional, List, Dict, Any, Type
from sqlalchemy.orm import Session

from app.infrastructure.database.repository import BaseRepository
from app.infrastructure.database.session import get_pg_session, get_mysql_session
from app.domain.knowledge_base.models.knowledge_base import KnowledgeBase
from app.shared.schemas.knowledge_base import KnowledgeBaseCreate, KnowledgeBaseUpdate


class KnowledgeBaseRepository(BaseRepository[KnowledgeBase, KnowledgeBaseCreate, KnowledgeBaseUpdate]):
    """知识库仓储"""
    
    def __init__(self):
        super().__init__(KnowledgeBase)
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return get_pg_session()
    
    def get_by_owner(self, owner_id: str, skip: int = 0, limit: int = 100) -> List[KnowledgeBase]:
        """根据拥有者获取知识库"""
        return self.get_multi(
            skip=skip,
            limit=limit,
            filters={"owner_id": owner_id}
        )
    
    def get_by_visibility(self, visibility: str, skip: int = 0, limit: int = 100) -> List[KnowledgeBase]:
        """根据可见性获取知识库"""
        return self.get_multi(
            skip=skip,
            limit=limit,
            filters={"visibility": visibility}
        )
    
    def get_public_knowledge_bases(self, skip: int = 0, limit: int = 100) -> List[KnowledgeBase]:
        """获取公开知识库"""
        return self.get_by_visibility("public", skip, limit)
    
    def search_by_name(self, name: str, skip: int = 0, limit: int = 100) -> List[KnowledgeBase]:
        """根据名称搜索知识库"""
        return self.get_multi(
            skip=skip,
            limit=limit,
            filters={"name": {"like": name}}
        )
    
    def get_user_accessible_knowledge_bases(self, user_id: str, skip: int = 0, limit: int = 100) -> List[KnowledgeBase]:
        """获取用户可访问的知识库"""
        # 这里应该包含用户拥有的、作为成员的、以及公开的知识库
        # 实际实现需要根据具体的权限模型来处理
        try:
            with self.get_session() as db:
                # 用户拥有的知识库
                owned_kbs = db.query(self.model).filter(self.model.owner_id == user_id).all()
                
                # 公开的知识库
                public_kbs = db.query(self.model).filter(self.model.visibility == "public").all()
                
                # 合并并去重
                all_kbs = {kb.id: kb for kb in owned_kbs + public_kbs}
                kb_list = list(all_kbs.values())
                
                # 分页
                return kb_list[skip:skip + limit]
        except Exception as e:
            from app.core.exceptions import DatabaseException
            raise DatabaseException(f"获取用户可访问知识库失败: {str(e)}") from e
    
    def update_last_accessed(self, knowledge_base_id: int, user_id: str) -> bool:
        """更新最后访问时间"""
        try:
            with self.get_session() as db:
                kb = db.query(self.model).filter(self.model.id == knowledge_base_id).first()
                if kb:
                    from datetime import datetime
                    kb.set_metadata("last_accessed", datetime.utcnow().isoformat())
                    kb.set_metadata("last_accessed_by", user_id)
                    db.commit()
                    return True
                return False
        except Exception as e:
            from app.core.exceptions import DatabaseException
            raise DatabaseException(f"更新知识库访问时间失败: {str(e)}") from e
    
    def get_statistics(self, knowledge_base_id: int) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            with self.get_session() as db:
                kb = db.query(self.model).filter(self.model.id == knowledge_base_id).first()
                if not kb:
                    return {}
                
                # 这里需要根据实际的文档模型来实现统计
                # 暂时返回基本统计信息
                stats = {
                    "id": kb.id,
                    "name": kb.name,
                    "created_at": kb.created_at.isoformat() if kb.created_at else None,
                    "updated_at": kb.updated_at.isoformat() if kb.updated_at else None,
                    "owner_id": kb.owner_id,
                    "visibility": kb.visibility,
                    "document_count": 0,  # 需要实际统计
                    "total_size": 0,  # 需要实际统计
                    "last_accessed": kb.get_metadata("last_accessed"),
                    "last_accessed_by": kb.get_metadata("last_accessed_by"),
                }
                
                return stats
        except Exception as e:
            from app.core.exceptions import DatabaseException
            raise DatabaseException(f"获取知识库统计信息失败: {str(e)}") from e