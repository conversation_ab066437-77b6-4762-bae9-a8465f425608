import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.exc import SQLAlchemyError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.api.api import api_router
from app.api.middleware.api_middleware import APIVersionMiddleware, APIRateLimitMiddleware, APILoggingMiddleware
from app.core.config.config import settings
from app.core.exceptions.exceptions import (
    BioHubException,
    biohub_exception_handler,
    general_exception_handler,
    http_exception_handler,
    sqlalchemy_exception_handler,
    validation_exception_handler,
)
from app.core.logging.log_config import get_logger, setup_logging
from app.api.middleware.middleware import (
    AuthenticationMiddleware,
    LoggingMiddleware,
    RequestContextMiddleware,
)
from app.infrastructure.database.base import init_db
from app.core.exceptions.handlers import (
    exception_handler,
    biohub_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    sqlalchemy_exception_handler,
    general_exception_handler,
)
from app.core.exceptions.business import (
    AuthenticationException,
    ValidationException,
    BusinessException,
    DatabaseException,
)

# 初始化日志系统
setup_logging()
logger = get_logger("app.main")

app = FastAPI(
    title="Biohub AI",
    description="A agent for BIO",
    version="Alpha",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    prefix="/ai",
)

# 添加中间件（注意顺序：后添加的先执行）
app.add_middleware(RequestContextMiddleware)
app.add_middleware(AuthenticationMiddleware)
app.add_middleware(LoggingMiddleware)
app.add_middleware(APIVersionMiddleware)
app.add_middleware(APIRateLimitMiddleware, calls=100, period=60)
app.add_middleware(APILoggingMiddleware)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册异常处理器
app.add_exception_handler(AuthenticationException, biohub_exception_handler)
app.add_exception_handler(ValidationException, biohub_exception_handler)
app.add_exception_handler(BusinessException, biohub_exception_handler)
app.add_exception_handler(DatabaseException, biohub_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    logger.info("应用启动中...")
    try:
        init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}", exc_info=True)
        raise
    logger.info("应用启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的事件处理"""
    logger.info("应用正在关闭...")
    logger.info("应用已关闭")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=settings.WORKERS,
    )
